from fastapi import FastAP<PERSON>
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi import Request
from crm_project.database import init_db
from crm_project.routers import contacts, accounts, leads, tasks, webhooks, settings, auth
from crm_project.crud import get_user_by_username, create_user

app = FastAPI(title="PydanticAI CRM")

# Mount static files
app.mount("/static", StaticFiles(directory="crm_project/ui/static"), name="static")

# Jinja2 templates
templates = Jinja2Templates(directory="crm_project/templates")

@app.on_event("startup")
def on_startup():
    print("[DEBUG] on_startup called: initializing DB...")
    try:
        init_db()
        print("[DEBUG] DB initialized successfully.")
        # Create default admin if not exists
        from crm_project.database import SessionLocal
        db = SessionLocal()
        admin_user = get_user_by_username(db, "admin")
        if not admin_user:
            print("[DEBUG] Creating default admin user: admin / admin123")
            admin_user = create_user(db, username="admin", email="<EMAIL>", password="admin123", role="admin")
        # Save admin login info to a file
        with open("admin_login.txt", "w") as f:
            f.write(f"Admin login:\nUsername: admin\nPassword: admin123\nEmail: <EMAIL>\n")
        # Create two test users
        if not get_user_by_username(db, "user1"):
            create_user(db, username="user1", email="<EMAIL>", password="user123", role="user")
        if not get_user_by_username(db, "user2"):
            create_user(db, username="user2", email="<EMAIL>", password="user123", role="user")
        db.close()
    except Exception as e:
        print(f"[ERROR] DB initialization failed: {e}")
        import sys
        sys.exit(1)

@app.get("/", response_class=HTMLResponse)
def root(request: Request):
    return templates.TemplateResponse("home.html", {"request": request})

@app.get("/app", response_class=HTMLResponse)
def crm_app(request: Request):
    return templates.TemplateResponse("crm_app.html", {"request": request})

@app.get("/webhooks-ui", response_class=HTMLResponse)
def webhooks_ui(request: Request):
    return templates.TemplateResponse("webhooks_ui.html", {"request": request})

@app.get("/tasks-management", response_class=HTMLResponse)
def task_management_ui(request: Request):
    return templates.TemplateResponse("tasks_management.html", {"request": request})

@app.get("/contacts-enhanced", response_class=HTMLResponse)
def contacts_enhanced_ui(request: Request):
    return templates.TemplateResponse("enhanced_contacts.html", {"request": request})

@app.get("/leads-pipeline", response_class=HTMLResponse)
def leads_pipeline_ui(request: Request):
    return templates.TemplateResponse("leads_pipeline.html", {"request": request})

app.include_router(contacts.router)
app.include_router(accounts.router)
app.include_router(leads.router)
app.include_router(tasks.router)
app.include_router(webhooks.router)
app.include_router(settings.router)
app.include_router(auth.router)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("crm_project.main:app", host="127.0.0.1", reload=True)

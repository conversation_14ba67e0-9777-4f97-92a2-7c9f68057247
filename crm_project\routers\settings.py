from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from crm_project.database import get_session
from crm_project.crud import get_setting, set_setting

router = APIRouter(prefix="/settings", tags=["Settings"])

@router.get("/relay_webhook_url")
def get_relay_webhook_url(db: Session = Depends(get_session)):
    url = get_setting(db, "relay_webhook_url")
    return {"relay_webhook_url": url}

@router.post("/relay_webhook_url")
def set_relay_webhook_url(url: str, db: Session = Depends(get_session)):
    set_setting(db, "relay_webhook_url", url)
    return {"relay_webhook_url": url}

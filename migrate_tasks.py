#!/usr/bin/env python3
"""
Database migration script to enhance the tasks table with new fields for comprehensive task management.
This script adds all the new columns required for the enhanced Task & Activity Management System.
"""

import sqlite3
import sys
from datetime import datetime

def migrate_tasks_table():
    """Migrate the tasks table to include enhanced task management fields"""
    
    try:
        # Connect to the database
        conn = sqlite3.connect('crm.db')
        cursor = conn.cursor()
        
        print("Starting tasks table migration...")
        
        # Check current table structure
        cursor.execute("PRAGMA table_info(tasks)")
        existing_columns = [column[1] for column in cursor.fetchall()]
        print(f"Current columns: {existing_columns}")
        
        # Define new columns to add
        new_columns = [
            ("title", "ALTER TABLE tasks ADD COLUMN title TEXT"),
            ("task_type", "ALTER TABLE tasks ADD COLUMN task_type TEXT DEFAULT 'general'"),
            ("priority", "ALTER TABLE tasks ADD COLUMN priority TEXT DEFAULT 'medium'"),
            ("status", "ALTER TABLE tasks ADD COLUMN status TEXT DEFAULT 'pending'"),
            ("start_date", "ALTER TABLE tasks ADD COLUMN start_date DATETIME"),
            ("estimated_duration", "ALTER TABLE tasks ADD COLUMN estimated_duration INTEGER"),
            ("lead_id", "ALTER TABLE tasks ADD COLUMN lead_id INTEGER"),
            ("account_id", "ALTER TABLE tasks ADD COLUMN account_id INTEGER"),
            ("tags", "ALTER TABLE tasks ADD COLUMN tags JSON DEFAULT '[]'"),
            ("notes", "ALTER TABLE tasks ADD COLUMN notes TEXT"),
            ("completed_at", "ALTER TABLE tasks ADD COLUMN completed_at DATETIME"),
            ("updated_at", "ALTER TABLE tasks ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP")
        ]
        
        # Add new columns if they don't exist
        for column_name, alter_statement in new_columns:
            if column_name not in existing_columns:
                print(f"Adding column: {column_name}")
                cursor.execute(alter_statement)
            else:
                print(f"Column {column_name} already exists, skipping...")
        
        # Update existing tasks to have titles if they don't have them
        print("Updating existing tasks with titles...")
        cursor.execute("""
            UPDATE tasks 
            SET title = COALESCE(title, 'Task #' || id)
            WHERE title IS NULL OR title = ''
        """)
        
        # Update existing tasks with default values for new fields
        print("Setting default values for existing tasks...")
        cursor.execute("""
            UPDATE tasks 
            SET 
                task_type = COALESCE(task_type, 'general'),
                priority = COALESCE(priority, 'medium'),
                status = CASE 
                    WHEN completed = 1 THEN 'completed'
                    ELSE COALESCE(status, 'pending')
                END,
                updated_at = COALESCE(updated_at, created_at),
                tags = COALESCE(tags, '[]')
            WHERE task_type IS NULL OR priority IS NULL OR status IS NULL OR updated_at IS NULL OR tags IS NULL
        """)
        
        # Set completed_at for already completed tasks
        print("Setting completed_at for existing completed tasks...")
        cursor.execute("""
            UPDATE tasks 
            SET completed_at = created_at
            WHERE completed = 1 AND completed_at IS NULL
        """)
        
        # Add foreign key constraints (SQLite doesn't support adding FK constraints to existing tables,
        # but we can create indexes for performance)
        print("Creating indexes for foreign key relationships...")
        
        try:
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_lead_id ON tasks(lead_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_account_id ON tasks(account_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_priority ON tasks(priority)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_task_type ON tasks(task_type)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks(due_date)")
        except sqlite3.Error as e:
            print(f"Note: Some indexes may already exist: {e}")
        
        # Commit the changes
        conn.commit()
        
        # Verify the migration
        print("\nVerifying migration...")
        cursor.execute("PRAGMA table_info(tasks)")
        final_columns = [column[1] for column in cursor.fetchall()]
        print(f"Final columns: {final_columns}")
        
        # Count tasks
        cursor.execute("SELECT COUNT(*) FROM tasks")
        task_count = cursor.fetchone()[0]
        print(f"Total tasks in database: {task_count}")
        
        if task_count > 0:
            # Show sample of migrated data
            cursor.execute("""
                SELECT id, title, task_type, priority, status, completed, created_at 
                FROM tasks 
                LIMIT 3
            """)
            sample_tasks = cursor.fetchall()
            print("\nSample migrated tasks:")
            for task in sample_tasks:
                print(f"  ID: {task[0]}, Title: {task[1]}, Type: {task[2]}, Priority: {task[3]}, Status: {task[4]}")
        
        print("\n✅ Tasks table migration completed successfully!")
        print("The enhanced Task & Activity Management System is now ready to use.")
        
    except sqlite3.Error as e:
        print(f"❌ Database error during migration: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during migration: {e}")
        return False
    finally:
        if conn:
            conn.close()
    
    return True

if __name__ == "__main__":
    print("Enhanced Task Management Migration Script")
    print("=" * 50)
    
    # Run the migration
    success = migrate_tasks_table()
    
    if success:
        print("\n🎉 Migration completed successfully!")
        print("You can now use the enhanced Task & Activity Management features.")
        sys.exit(0)
    else:
        print("\n💥 Migration failed!")
        print("Please check the error messages above and try again.")
        sys.exit(1)

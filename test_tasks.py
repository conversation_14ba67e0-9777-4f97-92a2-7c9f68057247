import pytest
from fastapi.testclient import TestClient
from crm_project.main import app
from crm_project.models import User, Contact, Task
from crm_project.database import get_session
from sqlalchemy.orm import Session
from crm_project.test_utils import ensure_test_users

ensure_test_users()

def auth_header(username="admin", password="admin123"):
    client = TestClient(app)
    resp = client.post("/auth/login", data={"username": username, "password": password})
    if "access_token" not in resp.json():
        print("LOGIN RESPONSE:", resp.status_code, resp.text)
    token = resp.json().get("access_token")
    assert token, f"Login failed for {username}: {resp.text}"
    return {"Authorization": f"Bearer {token}"}

client = TestClient(app)

def setup_contact():
    db: Session = next(get_session())
    contact = db.query(Contact).filter_by(email="<EMAIL>").first()
    if not contact:
        contact = Contact(first_name="Task", last_name="Contact", email="<EMAIL>", phone="1234567890")
        db.add(contact)
        db.commit()
        db.refresh(contact)
    db.close()
    return contact

def setup_users():
    db: Session = next(get_session())
    from crm_project.crud import create_user, get_user_by_username, pwd_context
    admin = get_user_by_username(db, "admin")
    if not admin:
        admin = create_user(db, username="admin", email="<EMAIL>", password="admin123", role="admin")
        print("ADMIN HASH:", admin.password_hash)
        print("VERIFY admin123:", pwd_context.verify("admin123", admin.password_hash))
    else:
        print("ADMIN EXISTS HASH:", admin.password_hash)
        print("VERIFY admin123:", pwd_context.verify("admin123", admin.password_hash))
    user = get_user_by_username(db, "user")
    if not user:
        user = create_user(db, username="user", email="<EMAIL>", password="userpass", role="user")
        print("USER HASH:", user.password_hash)
        print("VERIFY userpass:", pwd_context.verify("userpass", user.password_hash))
    else:
        print("USER EXISTS HASH:", user.password_hash)
        print("VERIFY userpass:", pwd_context.verify("userpass", user.password_hash))
    db.close()

# Call setup_users at module import so users always exist for tests
setup_users()

# Utility to clean up tasks by title or contact after each test

def cleanup_tasks(db: Session, title: str = None, contact_id: int = None):
    query = db.query(Task)
    if title:
        query = query.filter(Task.title == title)
    if contact_id:
        query = query.filter(Task.contact_id == contact_id)
    deleted = query.delete(synchronize_session=False)
    db.commit()
    return deleted

def test_create_task():
    contact = setup_contact()
    payload = {
        "title": "Test Task",
        "description": "Test description",
        "contact_id": contact.id,
        "due_date": "2030-01-01T10:00:00",
        "priority": "high"
    }
    try:
        response = client.post("/tasks/", json=payload, headers=auth_header())
        assert response.status_code == 201
        data = response.json()
        assert data["title"] == "Test Task"
        assert data["contact_id"] == contact.id
    finally:
        db = next(get_session())
        cleanup_tasks(db, title="Test Task")
        db.close()

def test_duplicate_task():
    contact = setup_contact()
    payload = {
        "title": "Duplicate Task",
        "description": "Test description",
        "contact_id": contact.id,
        "due_date": "2030-01-01T10:00:00",
        "priority": "medium"
    }
    try:
        client.post("/tasks/", json=payload, headers=auth_header())
        response = client.post("/tasks/", json=payload, headers=auth_header())
        assert response.status_code == 400
        assert "Duplicate" in response.json()["detail"]
    finally:
        db = next(get_session())
        cleanup_tasks(db, title="Duplicate Task")
        db.close()

def test_list_tasks_pagination():
    contact = setup_contact()
    titles = []
    try:
        for i in range(10):
            title = f"Paginate Task {i}"
            titles.append(title)
            payload = {
                "title": title,
                "description": "Test description",
                "contact_id": contact.id,
                "due_date": f"2030-01-0{(i%9)+1}T10:00:00",
                "priority": "medium"
            }
            client.post("/tasks/", json=payload, headers=auth_header())
        response = client.get("/tasks/?skip=0&limit=5", headers=auth_header())
        assert response.status_code == 200
        assert len(response.json()) == 5
    finally:
        db = next(get_session())
        for title in titles:
            cleanup_tasks(db, title=title)
        db.close()

def test_update_task():
    contact = setup_contact()
    payload = {
        "title": "Update Task",
        "description": "Test description",
        "contact_id": contact.id,
        "due_date": "2030-01-02T10:00:00",
        "priority": "low"
    }
    try:
        create_resp = client.post("/tasks/", json=payload, headers=auth_header())
        task_id = create_resp.json()["id"]
        update_payload = {"priority": "urgent", "description": "Test description updated"}
        response = client.put(f"/tasks/{task_id}", json=update_payload, headers=auth_header())
        assert response.status_code == 200
        assert response.json()["priority"] == "urgent"
    finally:
        db = next(get_session())
        cleanup_tasks(db, title="Update Task")
        db.close()

def test_delete_task():
    contact = setup_contact()
    payload = {
        "title": "Delete Task",
        "description": "Test description",
        "contact_id": contact.id,
        "due_date": "2030-01-03T10:00:00",
        "priority": "medium"
    }
    try:
        create_resp = client.post("/tasks/", json=payload, headers=auth_header())
        task_id = create_resp.json()["id"]
        response = client.delete(f"/tasks/{task_id}", headers=auth_header())
        assert response.status_code == 200
        assert response.json() is True
    finally:
        db = next(get_session())
        cleanup_tasks(db, title="Delete Task")
        db.close()

def test_task_permissions():
    contact = setup_contact()
    payload = {
        "title": "Permission Task",
        "description": "Test description",
        "contact_id": contact.id,
        "due_date": "2030-01-04T10:00:00",
        "priority": "medium"
    }
    try:
        create_resp = client.post("/tasks/", json=payload, headers=auth_header())
        task_id = create_resp.json()["id"]
        # Simulate non-admin user
        user_header = auth_header(username="user", password="userpass")
        response = client.delete(f"/tasks/{task_id}", headers=user_header)
        assert response.status_code in (200, 403)  # 200 if user is owner, 403 if not
    finally:
        db = next(get_session())
        cleanup_tasks(db, title="Permission Task")
        db.close()

def test_task_detail():
    contact = setup_contact()
    payload = {
        "title": "Detail Task",
        "description": "Test description",
        "contact_id": contact.id,
        "due_date": "2030-01-05T10:00:00",
        "priority": "medium"
    }
    try:
        create_resp = client.post("/tasks/", json=payload, headers=auth_header())
        task_id = create_resp.json()["id"]
        response = client.get(f"/tasks/{task_id}", headers=auth_header())
        assert response.status_code == 200
        assert response.json()["title"] == "Detail Task"
    finally:
        db = next(get_session())
        cleanup_tasks(db, title="Detail Task")
        db.close()

def test_overdue_and_due_today():
    contact = setup_contact()
    # Overdue
    payload = {
        "title": "Overdue Task",
        "description": "Test description",
        "contact_id": contact.id,
        "due_date": "2000-01-01T10:00:00",
        "priority": "medium"
    }
    try:
        client.post("/tasks/", json=payload, headers=auth_header())
        response = client.get("/tasks/overdue", headers=auth_header())
        assert response.status_code == 200
        assert any(t["title"] == "Overdue Task" for t in response.json())
        # Due today
        from datetime import datetime
        today = datetime.utcnow().replace(hour=10, minute=0, second=0, microsecond=0)
        payload = {
            "title": "Today Task",
            "description": "Test description",
            "contact_id": contact.id,
            "due_date": today.isoformat(),
            "priority": "medium"
        }
        client.post("/tasks/", json=payload, headers=auth_header())
        response = client.get("/tasks/due-today", headers=auth_header())
        assert response.status_code == 200
        assert any(t["title"] == "Today Task" for t in response.json())
    finally:
        db = next(get_session())
        cleanup_tasks(db, title="Overdue Task")
        cleanup_tasks(db, title="Today Task")
        db.close()

def test_task_summary():
    # Clean up all test tasks for summary
    db = next(get_session())
    cleanup_tasks(db, title="Test Task")
    cleanup_tasks(db, title="Duplicate Task")
    cleanup_tasks(db, title="Update Task")
    cleanup_tasks(db, title="Delete Task")
    cleanup_tasks(db, title="Permission Task")
    cleanup_tasks(db, title="Detail Task")
    cleanup_tasks(db, title="Overdue Task")
    cleanup_tasks(db, title="Today Task")
    for i in range(10):
        cleanup_tasks(db, title=f"Paginate Task {i}")
    db.close()
    response = client.get("/tasks/summary", headers=auth_header())
    assert response.status_code == 200
    assert "total_tasks" in response.json()

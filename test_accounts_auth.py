import pytest
from fastapi.testclient import TestClient
from crm_project.main import app
from crm_project.database import get_session
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from crm_project.database import Base
import os

SQLALCHEMY_DATABASE_URL = "sqlite:///./test_crm.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base.metadata.create_all(bind=engine)

def override_get_session():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_session] = override_get_session
client = TestClient(app)

def test_accounts_requires_auth():
    resp = client.get("/accounts/")
    assert resp.status_code == 401 or resp.status_code == 403
    resp = client.post("/accounts/", json={"name": "NoAuth", "industry": "IT", "website": "https://noauth.com"})
    assert resp.status_code == 401 or resp.status_code == 403

def test_delete_requires_admin():
    # Simulate non-admin token (should be replaced with real token logic in integration)
    resp = client.delete("/accounts/1", headers={"Authorization": "Bearer testtoken"})
    assert resp.status_code in (403, 404, 401)

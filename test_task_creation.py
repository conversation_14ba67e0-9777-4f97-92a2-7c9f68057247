#!/usr/bin/env python3
"""
Test script to verify task creation functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from crm_project.database import get_session, init_db
from crm_project.schemas import TaskCreate
from crm_project.crud import create_task, get_tasks
from datetime import datetime

def test_task_creation():
    """Test creating a task to verify the functionality works"""
    
    print("Testing Task Creation...")
    print("=" * 40)
    
    # Initialize database
    init_db()
    
    # Get database session
    db = next(get_session())
    
    try:
        # Test 1: Create a simple task
        print("Test 1: Creating a simple task...")
        task_data = TaskCreate(
            title="Test Task",
            description="This is a test task",
            task_type="general",
            priority="medium",
            status="pending"
        )
        
        task = create_task(db, task_data)
        print(f"✅ Task created successfully: ID={task.id}, Title='{task.title}'")
        
        # Test 2: Create a task with due date
        print("\nTest 2: Creating a task with due date...")
        task_data2 = TaskCreate(
            title="Task with Due Date",
            description="This task has a due date",
            task_type="call",
            priority="high",
            due_date=datetime(2024, 12, 31, 15, 30),
            estimated_duration=30,
            tags=["urgent", "client-call"]
        )
        
        task2 = create_task(db, task_data2)
        print(f"✅ Task with due date created: ID={task2.id}, Title='{task2.title}'")
        
        # Test 3: List all tasks
        print("\nTest 3: Listing all tasks...")
        all_tasks = get_tasks(db)
        print(f"✅ Found {len(all_tasks)} tasks in database")
        
        for task in all_tasks:
            print(f"  - ID: {task.id}, Title: {task.title}, Type: {task.task_type}, Priority: {task.priority}")
        
        print("\n🎉 All tests passed! Task creation is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = test_task_creation()
    sys.exit(0 if success else 1)

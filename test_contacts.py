import pytest
from fastapi.testclient import TestClient
from crm_project.main import app
from crm_project.database import get_session
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from crm_project.database import Base
from crm_project.test_utils import ensure_test_users

SQLALCHEMY_DATABASE_URL = "sqlite:///./test_crm.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base.metadata.create_all(bind=engine)

def override_get_session():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_session] = override_get_session
client = TestClient(app)

# Ensure test users exist
ensure_test_users()

def get_jwt_token():
    client.post("/auth/register", json={"username": "testuser2", "email": "<EMAIL>", "password": "testpass"})
    resp = client.post("/auth/login", data={"username": "testuser2", "password": "testpass"})
    assert resp.status_code == 200, resp.text
    return resp.json()["access_token"]

def auth_header():
    return {"Authorization": f"Bearer {get_jwt_token()}"}

def test_create_contact():
    response = client.post("/contacts/", json={"first_name": "John", "last_name": "Doe", "email": "<EMAIL>", "phone": "1234567890"}, headers=auth_header())
    assert response.status_code in (201, 400)  # 400 if duplicate

def test_list_contacts():
    response = client.get("/contacts/", headers=auth_header())
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_get_contact():
    create_resp = client.post("/contacts/", json={"first_name": "Jane", "last_name": "Smith", "email": "<EMAIL>", "phone": "9876543210"}, headers=auth_header())
    if create_resp.status_code == 400:
        list_resp = client.get("/contacts/", headers=auth_header())
        acc = next((a for a in list_resp.json() if a["email"] == "<EMAIL>"), None)
        contact_id = acc["id"] if acc else 1
    else:
        contact_id = create_resp.json()["id"]
    response = client.get(f"/contacts/{contact_id}", headers=auth_header())
    assert response.status_code == 200

def test_update_contact():
    create_resp = client.post("/contacts/", json={"first_name": "Update", "last_name": "Contact", "email": "<EMAIL>", "phone": "5555555555"}, headers=auth_header())
    if create_resp.status_code == 400:
        list_resp = client.get("/contacts/", headers=auth_header())
        acc = next((a for a in list_resp.json() if a["email"] == "<EMAIL>"), None)
        contact_id = acc["id"] if acc else 1
    else:
        contact_id = create_resp.json()["id"]
    response = client.put(f"/contacts/{contact_id}", json={"first_name": "Updated", "last_name": "Contact", "email": "<EMAIL>", "phone": "5555555555"}, headers=auth_header())
    assert response.status_code in (200, 404, 400)
    # Try duplicate email
    client.post("/contacts/", json={"first_name": "Dup", "last_name": "Email", "email": "<EMAIL>", "phone": "1111111111"}, headers=auth_header())
    response_dup = client.put(f"/contacts/{contact_id}", json={"first_name": "Updated", "last_name": "Contact", "email": "<EMAIL>", "phone": "5555555555"}, headers=auth_header())
    assert response_dup.status_code == 400

def test_delete_contact():
    create_resp = client.post("/contacts/", json={"first_name": "Delete", "last_name": "Me", "email": "<EMAIL>", "phone": "2222222222"}, headers=auth_header())
    if create_resp.status_code == 400:
        list_resp = client.get("/contacts/", headers=auth_header())
        acc = next((a for a in list_resp.json() if a["email"] == "<EMAIL>"), None)
        contact_id = acc["id"] if acc else 1
    else:
        contact_id = create_resp.json()["id"]
    # Try as normal user (should fail with 403)
    response = client.delete(f"/contacts/{contact_id}", headers=auth_header())
    assert response.status_code in (403, 404)

def test_contact_activities_pagination():
    """Test /contacts/{contact_id}/activities endpoint with pagination and auth."""
    # Create a contact
    contact_resp = client.post(
        "/contacts/",
        json={"first_name": "Act", "last_name": "Test", "email": "<EMAIL>", "phone": "3333333333"},
        headers=auth_header()
    )
    if contact_resp.status_code == 400:
        # Already exists, get ID
        list_resp = client.get("/contacts/", headers=auth_header())
        acc = next((a for a in list_resp.json() if a["email"] == "<EMAIL>"), None)
        contact_id = acc["id"] if acc else 1
    else:
        contact_id = contact_resp.json()["id"]

    # Clean up any existing activities for this contact
    from crm_project.database import get_session
    from crm_project.models import ContactActivity
    db = next(override_get_session())
    db.query(ContactActivity).filter(ContactActivity.contact_id == contact_id).delete()
    db.commit()

    # Insert exactly 15 activities
    for i in range(15):
        activity = ContactActivity(contact_id=contact_id, activity_type="call", description=f"Test call {i}")
        db.add(activity)
    db.commit()
    db.close()

    # Test pagination: limit=5, skip=0
    resp = client.get(f"/contacts/{contact_id}/activities?skip=0&limit=5", headers=auth_header())
    assert resp.status_code == 200
    data = resp.json()
    assert isinstance(data, list)
    assert len(data) == 5
    # Test skip=5, limit=5
    resp2 = client.get(f"/contacts/{contact_id}/activities?skip=5&limit=5", headers=auth_header())
    assert resp2.status_code == 200
    data2 = resp2.json()
    assert len(data2) == 5
    # Test skip beyond available
    resp3 = client.get(f"/contacts/{contact_id}/activities?skip=100&limit=5", headers=auth_header())
    assert resp3.status_code == 200
    data3 = resp3.json()
    assert len(data3) == 0
    # Test without auth
    resp4 = client.get(f"/contacts/{contact_id}/activities?skip=0&limit=5")
    assert resp4.status_code == 401

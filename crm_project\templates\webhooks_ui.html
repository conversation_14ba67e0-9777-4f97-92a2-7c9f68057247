<!DOCTYPE html>
<html>
<head>
    <title>Webhook Management - CRM</title>
    <link rel="stylesheet" href="/static/css/base.css">
</head>
<body>
    <div class="nav">
        <a href="/">← Back to Home</a>
        <a href="/app">CRM App</a>
        <a href="/docs">API Docs</a>
    </div>
    <h1>Webhook Management</h1>
    <h2>Create New Webhook</h2>
    <form id="webhookForm">
        <!-- ...form fields... -->
    </form>
    <h2>Existing Webhooks</h2>
    <table id="webhooksTable">
        <thead>
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>URL</th>
                <th>Events</th>
                <th>Status</th>
                <th>Last Triggered</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody></tbody>
    </table>
    <h2>Relay.app Webhook Integration</h2>
    <form id="relayWebhookForm">
        <input type="url" id="relayWebhookUrl" placeholder="Paste your Relay.app webhook URL here" style="width:400px" required />
        <button type="submit">Save</button>
        <span id="relayWebhookStatus" style="margin-left:1em;"></span>
    </form>
    <h2>Send Contact to Relay.app Flow</h2>
    <form id="sendContactForm">
        <div class="form-group">
            <label for="contactSelect">Select Contact:</label>
            <select id="contactSelect" required style="width:400px"></select>
        </div>
        <div class="form-group">
            <label for="relaySendUrl">Relay.app Webhook URL:</label>
            <input type="url" id="relaySendUrl" placeholder="Paste or select your Relay.app webhook URL" style="width:400px" required />
        </div>
        <button type="submit">Send Contact</button>
        <span id="sendContactStatus" style="margin-left:1em;"></span>
    </form>
    <script src="/static/js/webhooks_ui.js"></script>
</body>
</html>

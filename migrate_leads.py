#!/usr/bin/env python3
"""
Database migration script to add new columns to the leads table
"""
import sqlite3
import os

def migrate_leads_table():
    """Add new columns to the leads table to match the enhanced Lead model"""
    
    # Connect to the database
    db_path = "crm.db"
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found!")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check current table structure
        cursor.execute("PRAGMA table_info(leads)")
        existing_columns = [row[1] for row in cursor.fetchall()]
        print(f"Existing columns: {existing_columns}")
        
        # Define new columns to add
        new_columns = [
            ("title", "VARCHAR"),
            ("description", "TEXT"),
            ("probability", "INTEGER DEFAULT 50"),
            ("value", "INTEGER DEFAULT 0"),
            ("expected_close_date", "DATE"),
            ("source", "VARCHAR"),
            ("campaign", "VARCHAR"),
            ("status", "VARCHAR DEFAULT 'open'"),
            ("priority", "VARCHAR DEFAULT 'medium'"),
            ("tags", "JSON"),
            ("custom_fields", "JSON"),
            ("closed_at", "DATETIME"),
            ("account_id", "INTEGER"),
            ("updated_at", "DATETIME")
        ]
        
        # Add missing columns
        for column_name, column_type in new_columns:
            if column_name not in existing_columns:
                try:
                    alter_sql = f"ALTER TABLE leads ADD COLUMN {column_name} {column_type}"
                    print(f"Adding column: {alter_sql}")
                    cursor.execute(alter_sql)
                    print(f"✓ Added column: {column_name}")
                except sqlite3.Error as e:
                    print(f"✗ Error adding column {column_name}: {e}")
            else:
                print(f"✓ Column {column_name} already exists")
        
        # Add stage column if it doesn't exist (with default value)
        if "stage" not in existing_columns:
            try:
                cursor.execute("ALTER TABLE leads ADD COLUMN stage VARCHAR DEFAULT 'prospect'")
                print("✓ Added column: stage")
            except sqlite3.Error as e:
                print(f"✗ Error adding stage column: {e}")
        else:
            print("✓ Column stage already exists")
        
        # Commit changes
        conn.commit()
        print("\n✓ Migration completed successfully!")
        
        # Show final table structure
        cursor.execute("PRAGMA table_info(leads)")
        final_columns = cursor.fetchall()
        print("\nFinal table structure:")
        for col in final_columns:
            print(f"  {col[1]} ({col[2]})")
        
        return True
        
    except sqlite3.Error as e:
        print(f"✗ Migration failed: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("Starting leads table migration...")
    success = migrate_leads_table()
    if success:
        print("\nMigration completed! You can now restart the server.")
    else:
        print("\nMigration failed! Please check the errors above.")

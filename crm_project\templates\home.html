<!DOCTYPE html>
<html>
<head>
    <title>PydanticAI CRM Home</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/base.css">
    <style>
        :root {
            --primary: #007bff;
            --primary-dark: #0056b3;
            --accent: #28a745;
            --danger: #dc3545;
            --bg: #f7f9fb;
            --surface: #fff;
            --text: #222;
            --muted: #6c757d;
            --border: #e0e0e0;
        }
        body {
            min-height: 100vh;
            background: var(--bg);
            font-family: 'Inter', 'Roboto', Arial, sans-serif;
            color: var(--text);
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: var(--surface);
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            color: var(--text);
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        .header p {
            color: var(--muted);
            font-size: 16px;
        }
        .login-section {
            background: var(--surface);
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }
        .login-section h2 {
            text-align: center;
            margin-bottom: 20px;
            color: var(--text);
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: var(--text);
            font-weight: 500;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border);
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .btn {
            background: var(--primary);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            width: 100%;
            margin-top: 10px;
        }
        .btn:hover {
            background: var(--primary-dark);
        }
        .nav-section {
            background: var(--surface);
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .nav-section h2 {
            text-align: center;
            margin-bottom: 20px;
            color: var(--text);
        }
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        .nav-card {
            background: var(--bg);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid var(--border);
            text-decoration: none;
            color: var(--text);
            transition: all 0.2s;
        }
        .nav-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .nav-card h3 {
            margin: 0 0 10px 0;
            color: var(--primary);
        }
        .nav-card p {
            margin: 0;
            color: var(--muted);
            font-size: 14px;
        }
        .status {
            text-align: center;
            margin: 10px 0;
            padding: 10px;
            border-radius: 6px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .admin-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 PydanticAI CRM</h1>
            <p>Comprehensive Customer Relationship Management System</p>
        </div>

        <div class="login-section">
            <h2>🔐 Login</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" class="btn">Login</button>
            </form>
            <div id="loginStatus" class="status" style="display: none;"></div>
            <div id="loginError" class="status error" style="display: none;"></div>

            <div class="admin-info">
                <strong>Default Admin Credentials:</strong><br>
                Username: admin<br>
                Password: admin123
            </div>
        </div>

        <div class="nav-section">
            <h2>📱 CRM Applications</h2>
            <div class="nav-grid">
                <a href="/app" class="nav-card">
                    <h3>👥 Basic CRM</h3>
                    <p>Simple contact management interface</p>
                </a>
                <a href="/contacts-enhanced" class="nav-card">
                    <h3>📇 Enhanced Contacts</h3>
                    <p>Advanced contact management with detailed profiles</p>
                </a>
                <a href="/leads-pipeline" class="nav-card">
                    <h3>🎯 Lead Pipeline</h3>
                    <p>Visual lead management and sales pipeline</p>
                </a>
                <a href="/tasks-management" class="nav-card">
                    <h3>📋 Task Management</h3>
                    <p>Organize and track your tasks and activities</p>
                </a>
                <a href="/webhooks-ui" class="nav-card">
                    <h3>🔗 Webhooks</h3>
                    <p>Configure webhook integrations and automation</p>
                </a>
                <a href="/docs" class="nav-card">
                    <h3>📚 API Documentation</h3>
                    <p>Interactive API documentation and testing</p>
                </a>
            </div>
        </div>
    </div>

    <script>
        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            const statusDiv = document.getElementById('loginStatus');
            const errorDiv = document.getElementById('loginError');

            // Hide previous messages
            statusDiv.style.display = 'none';
            errorDiv.style.display = 'none';

            try {
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
                });

                if (response.ok) {
                    const data = await response.json();
                    localStorage.setItem('crm_jwt', data.access_token);
                    statusDiv.textContent = 'Login successful! You can now access the CRM applications.';
                    statusDiv.className = 'status success';
                    statusDiv.style.display = 'block';

                    // Clear form
                    document.getElementById('loginForm').reset();
                } else {
                    const error = await response.json();
                    errorDiv.textContent = error.detail || 'Login failed';
                    errorDiv.style.display = 'block';
                }
            } catch (error) {
                console.error('Login error:', error);
                errorDiv.textContent = 'Login error. Please try again.';
                errorDiv.style.display = 'block';
            }
        });

        // Check if already logged in
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('crm_jwt');
            if (token) {
                const statusDiv = document.getElementById('loginStatus');
                statusDiv.textContent = 'You are already logged in. You can access the CRM applications.';
                statusDiv.className = 'status success';
                statusDiv.style.display = 'block';
            }
        });
    </script>
</body>
</html>

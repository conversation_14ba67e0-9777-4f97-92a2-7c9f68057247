// Task Management JavaScript
// Helper to get JWT from localStorage
function getAuthHeaders() {
    const token = localStorage.getItem('crm_jwt');
    return token ? { 'Authorization': 'Bearer ' + token } : {};
}

// Check if user is authenticated
function checkAuth() {
    const token = localStorage.getItem('crm_jwt');
    if (!token) {
        alert('Please login first. Redirecting to home page...');
        window.location.href = '/';
        return false;
    }
    return true;
}

// Initialize the tasks management page
document.addEventListener('DOMContentLoaded', function() {
    if (!checkAuth()) return;
    
    // Create the tasks management UI
    document.body.innerHTML = `
        <div class="header">
            <div class="nav">
                <a href="/">← Back to Home</a>
                <a href="/app">CRM App</a>
                <a href="/contacts-enhanced">Contacts</a>
                <a href="/leads-pipeline">Leads</a>
                <a href="/webhooks-ui">Webhooks</a>
            </div>
            <h1>📋 Task Management</h1>
            <button class="btn" onclick="openCreateTaskModal()">+ New Task</button>
        </div>
        
        <div class="task-summary">
            <div class="summary-card">
                <h3>Total Tasks</h3>
                <div class="summary-number" id="totalTasks">0</div>
            </div>
            <div class="summary-card">
                <h3>Pending</h3>
                <div class="summary-number" id="pendingTasks">0</div>
            </div>
            <div class="summary-card">
                <h3>Overdue</h3>
                <div class="summary-number" id="overdueTasks">0</div>
            </div>
            <div class="summary-card">
                <h3>Completed</h3>
                <div class="summary-number" id="completedTasks">0</div>
            </div>
        </div>
        
        <div class="tasks-container">
            <div class="tasks-section">
                <h2>📅 Due Today</h2>
                <div id="todayTasks" class="task-list"></div>
            </div>
            
            <div class="tasks-section">
                <h2>⚠️ Overdue</h2>
                <div id="overdueTasks" class="task-list"></div>
            </div>
            
            <div class="tasks-section">
                <h2>📋 All Tasks</h2>
                <div id="allTasks" class="task-list"></div>
            </div>
        </div>
        
        <!-- Create Task Modal -->
        <div id="createTaskModal" class="modal" style="display: none;">
            <div class="modal-content">
                <span class="close" onclick="closeCreateTaskModal()">&times;</span>
                <h2>Create New Task</h2>
                <form id="createTaskForm">
                    <input type="text" id="taskTitle" placeholder="Task Title" required>
                    <textarea id="taskDescription" placeholder="Task Description"></textarea>
                    <input type="date" id="taskDueDate" required>
                    <select id="taskPriority">
                        <option value="low">Low Priority</option>
                        <option value="medium" selected>Medium Priority</option>
                        <option value="high">High Priority</option>
                        <option value="urgent">Urgent</option>
                    </select>
                    <button type="submit">Create Task</button>
                </form>
            </div>
        </div>
    `;
    
    // Load initial data
    loadTasks();
    loadTaskSummary();
});

// Load all tasks
async function loadTasks() {
    try {
        const response = await fetch('/tasks/', {
            headers: getAuthHeaders()
        });
        
        if (response.status === 401) {
            alert('Session expired. Please login again.');
            localStorage.removeItem('crm_jwt');
            window.location.href = '/';
            return;
        }
        
        if (response.ok) {
            const tasks = await response.json();
            displayTasks(tasks);
        } else {
            console.error('Failed to load tasks');
        }
    } catch (error) {
        console.error('Error loading tasks:', error);
    }
}

// Load task summary
async function loadTaskSummary() {
    try {
        const response = await fetch('/tasks/summary', {
            headers: getAuthHeaders()
        });
        
        if (response.ok) {
            const summary = await response.json();
            updateTaskSummary(summary);
        }
    } catch (error) {
        console.error('Error loading task summary:', error);
    }
}

// Display tasks in different sections
function displayTasks(tasks) {
    const today = new Date().toISOString().split('T')[0];
    const todayTasks = tasks.filter(task => task.due_date === today && task.status !== 'completed');
    const overdueTasks = tasks.filter(task => task.due_date < today && task.status !== 'completed');
    const allTasks = tasks;
    
    displayTaskList('todayTasks', todayTasks);
    displayTaskList('overdueTasks', overdueTasks);
    displayTaskList('allTasks', allTasks);
}

// Display task list in a container
function displayTaskList(containerId, tasks) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    container.innerHTML = '';
    
    if (tasks.length === 0) {
        container.innerHTML = '<div class="empty-state">No tasks found</div>';
        return;
    }
    
    tasks.forEach(task => {
        const taskElement = createTaskElement(task);
        container.appendChild(taskElement);
    });
}

// Create task element
function createTaskElement(task) {
    const taskDiv = document.createElement('div');
    taskDiv.className = `task-card priority-${task.priority} ${task.status === 'completed' ? 'completed' : ''}`;
    
    taskDiv.innerHTML = `
        <div class="task-header">
            <h3>${task.title}</h3>
            <div class="task-actions">
                ${task.status !== 'completed' ? 
                    `<button onclick="completeTask(${task.id})" class="btn-complete">✓</button>` : 
                    '<span class="completed-badge">✓ Completed</span>'
                }
                <button onclick="deleteTask(${task.id})" class="btn-delete">🗑️</button>
            </div>
        </div>
        <p class="task-description">${task.description || 'No description'}</p>
        <div class="task-meta">
            <span class="task-due">Due: ${new Date(task.due_date).toLocaleDateString()}</span>
            <span class="task-priority priority-${task.priority}">${task.priority.toUpperCase()}</span>
        </div>
    `;
    
    return taskDiv;
}

// Update task summary
function updateTaskSummary(summary) {
    document.getElementById('totalTasks').textContent = summary.total || 0;
    document.getElementById('pendingTasks').textContent = summary.pending || 0;
    document.getElementById('overdueTasks').textContent = summary.overdue || 0;
    document.getElementById('completedTasks').textContent = summary.completed || 0;
}

// Open create task modal
function openCreateTaskModal() {
    document.getElementById('createTaskModal').style.display = 'block';
}

// Close create task modal
function closeCreateTaskModal() {
    document.getElementById('createTaskModal').style.display = 'none';
    document.getElementById('createTaskForm').reset();
}

// Create new task
document.addEventListener('submit', async function(event) {
    if (event.target.id === 'createTaskForm') {
        event.preventDefault();
        
        const taskData = {
            title: document.getElementById('taskTitle').value,
            description: document.getElementById('taskDescription').value,
            due_date: document.getElementById('taskDueDate').value,
            priority: document.getElementById('taskPriority').value
        };
        
        try {
            const response = await fetch('/tasks/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...getAuthHeaders()
                },
                body: JSON.stringify(taskData)
            });
            
            if (response.ok) {
                closeCreateTaskModal();
                loadTasks();
                loadTaskSummary();
            } else {
                const error = await response.json();
                alert('Error creating task: ' + (error.detail || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error creating task:', error);
            alert('Error creating task. Check console for details.');
        }
    }
});

// Complete task
async function completeTask(taskId) {
    try {
        const response = await fetch(`/tasks/${taskId}/complete`, {
            method: 'PUT',
            headers: getAuthHeaders()
        });
        
        if (response.ok) {
            loadTasks();
            loadTaskSummary();
        } else {
            const error = await response.json();
            alert('Error completing task: ' + (error.detail || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error completing task:', error);
        alert('Error completing task. Check console for details.');
    }
}

// Delete task
async function deleteTask(taskId) {
    if (!confirm('Are you sure you want to delete this task?')) {
        return;
    }
    
    try {
        const response = await fetch(`/tasks/${taskId}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
        });
        
        if (response.ok) {
            loadTasks();
            loadTaskSummary();
        } else {
            const error = await response.json();
            alert('Error deleting task: ' + (error.detail || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error deleting task:', error);
        alert('Error deleting task. Check console for details.');
    }
}

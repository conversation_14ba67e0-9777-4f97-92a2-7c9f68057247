// CRM App JavaScript
// Helper to get JWT from localStorage
function getAuthHeaders() {
    const token = localStorage.getItem('crm_jwt');
    return token ? { 'Authorization': 'Bearer ' + token } : {};
}

// Check if user is authenticated
function checkAuth() {
    const token = localStorage.getItem('crm_jwt');
    if (!token) {
        alert('Please login first. Redirecting to home page...');
        window.location.href = '/';
        return false;
    }
    return true;
}

// Load contacts from API
async function loadContacts() {
    if (!checkAuth()) return;
    
    try {
        const response = await fetch('/contacts/', {
            headers: getAuthHeaders()
        });
        
        if (response.status === 401) {
            alert('Session expired. Please login again.');
            localStorage.removeItem('crm_jwt');
            window.location.href = '/';
            return;
        }
        
        if (response.ok) {
            const contacts = await response.json();
            displayContacts(contacts);
        } else {
            console.error('Failed to load contacts:', response.status);
            document.getElementById('contactsTable').innerHTML = '<tr><td colspan="6">Error loading contacts. Check console for details.</td></tr>';
        }
    } catch (error) {
        console.error('Error loading contacts:', error);
        document.getElementById('contactsTable').innerHTML = '<tr><td colspan="6">Error loading contacts. Check console for details.</td></tr>';
    }
}

// Display contacts in table
function displayContacts(contacts) {
    const tbody = document.querySelector('#contactsTable tbody');
    tbody.innerHTML = '';
    
    contacts.forEach(contact => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${contact.id}</td>
            <td>${contact.first_name} ${contact.last_name}</td>
            <td>${contact.email}</td>
            <td>${contact.phone || 'N/A'}</td>
            <td>${new Date(contact.created_at).toLocaleDateString()}</td>
            <td><button onclick="deleteContact(${contact.id})" class="delete-btn">Delete</button></td>
        `;
        tbody.appendChild(row);
    });
}

// Add new contact
async function addContact(event) {
    event.preventDefault();
    if (!checkAuth()) return;
    
    const formData = {
        first_name: document.getElementById('first_name').value,
        last_name: document.getElementById('last_name').value,
        email: document.getElementById('email').value,
        phone: document.getElementById('phone').value
    };
    
    try {
        const response = await fetch('/contacts/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...getAuthHeaders()
            },
            body: JSON.stringify(formData)
        });
        
        if (response.status === 401) {
            alert('Session expired. Please login again.');
            localStorage.removeItem('crm_jwt');
            window.location.href = '/';
            return;
        }
        
        if (response.ok) {
            document.getElementById('addContactForm').reset();
            loadContacts(); // Reload the contacts list
        } else {
            const error = await response.json();
            alert('Error adding contact: ' + (error.detail || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error adding contact:', error);
        alert('Error adding contact. Check console for details.');
    }
}

// Delete contact
async function deleteContact(contactId) {
    if (!checkAuth()) return;
    
    if (!confirm('Are you sure you want to delete this contact?')) {
        return;
    }
    
    try {
        const response = await fetch(`/contacts/${contactId}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
        });
        
        if (response.status === 401) {
            alert('Session expired. Please login again.');
            localStorage.removeItem('crm_jwt');
            window.location.href = '/';
            return;
        }
        
        if (response.ok) {
            loadContacts(); // Reload the contacts list
        } else {
            const error = await response.json();
            alert('Error deleting contact: ' + (error.detail || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error deleting contact:', error);
        alert('Error deleting contact. Check console for details.');
    }
}

// Initialize the app
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication on page load
    if (!checkAuth()) return;
    
    // Set up form submission
    document.getElementById('addContactForm').addEventListener('submit', addContact);
    
    // Load initial data
    loadContacts();
});

["test_accounts.py::test_create_account", "test_accounts.py::test_delete_account", "test_accounts.py::test_get_account", "test_accounts.py::test_list_accounts", "test_accounts.py::test_update_account", "test_accounts_auth.py::test_accounts_requires_auth", "test_accounts_auth.py::test_delete_requires_admin", "test_contacts.py::test_contact_activities_pagination", "test_contacts.py::test_create_contact", "test_contacts.py::test_delete_contact", "test_contacts.py::test_get_contact", "test_contacts.py::test_list_contacts", "test_contacts.py::test_update_contact", "test_leads.py::test_create_lead", "test_leads.py::test_delete_lead", "test_leads.py::test_get_lead_detail", "test_leads.py::test_lead_notes_pagination", "test_leads.py::test_list_leads_pagination", "test_leads.py::test_update_lead", "test_task_creation.py::test_task_creation", "test_tasks.py::test_complete_task_permission", "test_tasks.py::test_create_task", "test_tasks.py::test_delete_task", "test_tasks.py::test_delete_task_admin", "test_tasks.py::test_duplicate_task", "test_tasks.py::test_get_task_detail", "test_tasks.py::test_invalid_contact", "test_tasks.py::test_list_tasks_pagination", "test_tasks.py::test_overdue_and_due_today", "test_tasks.py::test_overdue_and_due_today_pagination", "test_tasks.py::test_task_detail", "test_tasks.py::test_task_permissions", "test_tasks.py::test_task_summary", "test_tasks.py::test_update_task", "test_tasks.py::test_update_task_permission", "test_webhooks.py::test_create_webhook", "test_webhooks.py::test_duplicate_webhook", "test_webhooks.py::test_list_webhooks_pagination", "test_webhooks.py::test_webhook_admin_required", "test_webhooks.py::test_webhook_secret_not_exposed", "test_webhooks.py::test_webhook_ssrf_protection"]
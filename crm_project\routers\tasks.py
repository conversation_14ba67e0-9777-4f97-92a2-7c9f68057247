from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Optional, Dict, Any, List
from crm_project.database import get_session
from crm_project.crud import (
    create_task, get_tasks, get_task, update_task, complete_task, delete_task,
    get_overdue_tasks, get_tasks_due_today, get_task_summary
)
from crm_project.schemas import TaskCreate, TaskRead, TaskUpdate, TaskDetailRead
from crm_project.jwt_utils import get_current_user, admin_required
from crm_project.models import Task

router = APIRouter(prefix="/tasks", tags=["Tasks"])

@router.post("/", response_model=TaskRead, status_code=201)
async def add_task(
    payload: TaskCreate,
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """Create a new task. Requires authentication. Checks for duplicates and valid references."""
    # Duplicate check: title + contact/lead/account + due_date
    q = db.query(Task).filter(Task.title == payload.title)
    if payload.contact_id:
        q = q.filter(Task.contact_id == payload.contact_id)
    if payload.lead_id:
        q = q.filter(Task.lead_id == payload.lead_id)
    if payload.account_id:
        q = q.filter(Task.account_id == payload.account_id)
    if payload.due_date:
        q = q.filter(Task.due_date == payload.due_date)
    if q.first():
        raise HTTPException(status_code=400, detail="Duplicate task for this entity and date")
    # TODO: Validate foreign keys exist
    return create_task(db, payload)

@router.get("/", response_model=List[TaskRead])
async def list_tasks(
    status: Optional[str] = Query(None, description="Filter by status"),
    priority: Optional[str] = Query(None, description="Filter by priority"),
    task_type: Optional[str] = Query(None, description="Filter by task type"),
    contact_id: Optional[int] = Query(None, description="Filter by contact"),
    lead_id: Optional[int] = Query(None, description="Filter by lead"),
    account_id: Optional[int] = Query(None, description="Filter by account"),
    skip: int = Query(0, ge=0, description="Records to skip for pagination"),
    limit: int = Query(20, ge=1, le=100, description="Max records to return"),
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """List tasks with optional filtering and pagination. Requires authentication."""
    return get_tasks(db, status, priority, task_type, contact_id, lead_id, account_id, skip, limit)

@router.get("/summary", response_model=Dict[str, Any])
async def get_tasks_summary(
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """Get task summary statistics. Requires authentication."""
    return get_task_summary(db)

@router.get("/overdue", response_model=List[TaskRead])
async def list_overdue_tasks(
    skip: int = Query(0, ge=0, description="Records to skip for pagination"),
    limit: int = Query(20, ge=1, le=100, description="Max records to return"),
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """Get all overdue tasks with pagination. Requires authentication."""
    return get_overdue_tasks(db, skip, limit)

@router.get("/due-today", response_model=List[TaskRead])
async def list_tasks_due_today(
    skip: int = Query(0, ge=0, description="Records to skip for pagination"),
    limit: int = Query(20, ge=1, le=100, description="Max records to return"),
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """Get all tasks due today with pagination. Requires authentication."""
    return get_tasks_due_today(db, skip, limit)

@router.get("/{task_id}", response_model=TaskDetailRead)
async def get_task_detail(
    task_id: int,
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """Get task details. Requires authentication."""
    task = get_task(db, task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    # Add related entity names
    task_detail = TaskDetailRead.from_orm(task)
    if task.contact:
        task_detail.contact_name = f"{task.contact.first_name} {task.contact.last_name}"
    if task.lead:
        task_detail.lead_title = task.lead.title
    if task.account:
        task_detail.account_name = task.account.name
    return task_detail

@router.put("/{task_id}", response_model=TaskRead)
async def update_task_endpoint(
    task_id: int,
    payload: TaskUpdate,
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """Update a task. Only the owner or admin can update."""
    task = get_task(db, task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    if current_user.role != "admin" and getattr(task, "owner_id", None) != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to update this task")
    updated = update_task(db, task_id, payload)
    if not updated:
        raise HTTPException(status_code=400, detail="Update failed")
    return updated

@router.put("/{task_id}/complete", response_model=TaskRead)
async def complete_task_endpoint(
    task_id: int,
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """Mark a task as completed. Only the owner or admin can complete."""
    task = get_task(db, task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    if current_user.role != "admin" and getattr(task, "owner_id", None) != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to complete this task")
    completed = complete_task(db, task_id)
    if not completed:
        raise HTTPException(status_code=400, detail="Complete failed")
    return completed

@router.delete("/{task_id}", response_model=bool)
async def remove_task(
    task_id: int,
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """Delete a task. Only admin or owner can delete."""
    task = get_task(db, task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    if current_user.role != "admin" and getattr(task, "owner_id", None) != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this task")
    return delete_task(db, task_id)

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from crm_project.database import get_session
from crm_project.crud import create_user, authenticate_user, get_user_by_username
from crm_project.jwt_utils import create_access_token, get_current_username, get_current_user
from pydantic import BaseModel
from fastapi.security import OAuth2PasswordRequestForm
from typing import Optional

router = APIRouter(prefix="/auth", tags=["Auth"])

class UserRegister(BaseModel):
    username: str
    email: str
    password: str

@router.post("/register")
def register_user(payload: UserRegister, db: Session = Depends(get_session)):
    if get_user_by_username(db, payload.username):
        raise HTTPException(status_code=400, detail="Username already exists")
    user = create_user(db, payload.username, payload.email, payload.password, role="user")
    return {"id": user.id, "username": user.username, "email": user.email, "role": user.role}

@router.post("/login")
def login_user(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_session)):
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")
    access_token = create_access_token({"sub": user.username, "role": user.role})
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/me")
def get_me(current_user=Depends(get_current_user)):
    return {"id": current_user.id, "username": current_user.username, "email": current_user.email, "role": current_user.role}

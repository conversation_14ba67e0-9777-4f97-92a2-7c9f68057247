/* CRM Design System - Base Styles */

:root {
    /* Pastel Color Palette */
    --primary: #E8F4FD;
    --primary-dark: #B3D9F7;
    --secondary: #F0E6FF;
    --secondary-dark: #D4B8FF;
    --success: #E8F5E8;
    --success-dark: #C6E6C6;
    --warning: #FFF4E6;
    --warning-dark: #FFE4B3;
    --error: #FFE6E6;
    --error-dark: #FFB3B3;
    --neutral: #F8F9FA;
    --neutral-dark: #E9ECEF;
    --text-primary: #2D3748;
    --text-secondary: #4A5568;
    --text-muted: #718096;
    --accent: #667EEA;
    --accent-dark: #5A67D8;
    --white: #FFFFFF;
    --border: #E2E8F0;
    --shadow: rgba(0, 0, 0, 0.1);
    
    /* Typography */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 var(--shadow);
    --shadow-md: 0 4px 6px -1px var(--shadow);
    --shadow-lg: 0 10px 15px -3px var(--shadow);
    --shadow-xl: 0 20px 25px -5px var(--shadow);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--neutral);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Layout */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.main-layout {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 280px;
    background: var(--white);
    border-right: 1px solid var(--border);
    box-shadow: var(--shadow-sm);
}

.content {
    flex: 1;
    padding: var(--spacing-xl);
    overflow-x: auto;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

/* Links */
a {
    color: var(--accent);
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover {
    color: var(--accent-dark);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    gap: var(--spacing-xs);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--accent);
    color: var(--white);
    border-color: var(--accent);
}

.btn-primary:hover:not(:disabled) {
    background: var(--accent-dark);
    border-color: var(--accent-dark);
    color: var(--white);
}

.btn-secondary {
    background: var(--secondary);
    color: var(--text-primary);
    border-color: var(--secondary-dark);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--secondary-dark);
    color: var(--text-primary);
}

.btn-success {
    background: var(--success);
    color: var(--text-primary);
    border-color: var(--success-dark);
}

.btn-success:hover:not(:disabled) {
    background: var(--success-dark);
    color: var(--text-primary);
}

.btn-warning {
    background: var(--warning);
    color: var(--text-primary);
    border-color: var(--warning-dark);
}

.btn-warning:hover:not(:disabled) {
    background: var(--warning-dark);
    color: var(--text-primary);
}

.btn-error {
    background: var(--error);
    color: var(--text-primary);
    border-color: var(--error-dark);
}

.btn-error:hover:not(:disabled) {
    background: var(--error-dark);
    color: var(--text-primary);
}

.btn-outline {
    background: transparent;
    color: var(--accent);
    border-color: var(--accent);
}

.btn-outline:hover:not(:disabled) {
    background: var(--accent);
    color: var(--white);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
}

/* Forms */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    background: var(--white);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* Cards */
.card {
    background: var(--white);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border);
    background: var(--neutral);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border);
    background: var(--neutral);
}

/* Utilities */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-none { display: none; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

.mb-0 { margin-bottom: 0; }
.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }

.mt-0 { margin-top: 0; }
.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }

.p-0 { padding: 0; }
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }
    
    .main-layout {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid var(--border);
    }
    
    .content {
        padding: var(--spacing-lg);
    }
}

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session
from crm_project.database import get_session
from crm_project.crud import (
    create_contact, get_contacts, get_contact, update_contact,
    create_contact_note, get_contact_notes, delete_contact_note,
    get_contact_activities
)
from crm_project.schemas import (
    ContactCreate, ContactRead, ContactUpdate, ContactDetailRead,
    ContactNoteCreate, ContactNoteRead, ContactActivityRead
)
from crm_project.enums import WebhookEvents
from crm_project.jwt_utils import get_current_user, admin_required
from crm_project.models import Contact
from crm_project.webhook_service import trigger_webhook_event
from typing import List

router = APIRouter(prefix="/contacts", tags=["Contacts"])

@router.post("/", response_model=ContactRead, status_code=201)
async def add_contact(
    payload: ContactCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """Create a new contact. Requires authentication. Handles duplicate email/phone."""
    # Explicit duplicate check
    if db.query(Contact).filter_by(email=payload.email).first():
        raise HTTPException(status_code=400, detail="Contact with this email already exists")
    if payload.phone and db.query(Contact).filter_by(phone=payload.phone).first():
        raise HTTPException(status_code=400, detail="Contact with this phone already exists")
    try:
        contact = create_contact(db, payload)
        contact_data = {
            "id": contact.id,
            "first_name": contact.first_name,
            "last_name": contact.last_name,
            "email": contact.email,
            "phone": contact.phone,
            "created_at": contact.created_at.isoformat()
        }
        background_tasks.add_task(trigger_webhook_event, WebhookEvents.CONTACT_CREATED, contact_data)
        return contact
    except IntegrityError:
        raise HTTPException(status_code=400, detail="Email already exists")

@router.get("/", response_model=list[ContactRead])
def list_contacts(
    db: Session = Depends(get_session),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user=Depends(get_current_user)
):
    """List contacts with pagination. Requires authentication."""
    return db.query(Contact).offset(skip).limit(limit).all()

@router.get("/{contact_id}", response_model=ContactDetailRead)
def get_one(contact_id: int, db: Session = Depends(get_session), current_user=Depends(get_current_user)):
    """Get contact by ID. Requires authentication."""
    contact = get_contact(db, contact_id)
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")
    contact_dict = contact.__dict__.copy()
    contact_dict['account_name'] = contact.account.name if contact.account else None
    return contact

@router.put("/{contact_id}", response_model=ContactRead)
async def update_contact_endpoint(
    contact_id: int,
    payload: ContactUpdate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """Update a contact. Requires authentication. Handles duplicate email/phone."""
    # Explicit duplicate check (excluding self)
    if db.query(Contact).filter(Contact.email == payload.email, Contact.id != contact_id).first():
        raise HTTPException(status_code=400, detail="Contact with this email already exists")
    if payload.phone and db.query(Contact).filter(Contact.phone == payload.phone, Contact.id != contact_id).first():
        raise HTTPException(status_code=400, detail="Contact with this phone already exists")
    contact = update_contact(db, contact_id, payload)
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")
    contact_data = {
        "id": contact.id,
        "first_name": contact.first_name,
        "last_name": contact.last_name,
        "email": contact.email,
        "phone": contact.phone,
        "job_title": contact.job_title,
        "department": contact.department,
        "updated_at": contact.updated_at.isoformat()
    }
    background_tasks.add_task(trigger_webhook_event, WebhookEvents.CONTACT_UPDATED, contact_data)
    return contact

@router.delete("/{contact_id}", status_code=200)
async def delete_contact(
    contact_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_session),
    current_user=Depends(admin_required)
):
    """Delete a contact. Admin only. Returns a confirmation message."""
    contact = get_contact(db, contact_id)
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")
    contact_data = {
        "id": contact.id,
        "first_name": contact.first_name,
        "last_name": contact.last_name,
        "email": contact.email,
        "phone": contact.phone
    }
    from crm_project.crud import delete_contact as crud_delete_contact
    success = crud_delete_contact(db, contact_id)
    if success:
        background_tasks.add_task(trigger_webhook_event, WebhookEvents.CONTACT_DELETED, contact_data)
        return {"detail": f"Contact {contact_id} deleted successfully"}
    raise HTTPException(status_code=404, detail="Contact not found")

@router.post("/{contact_id}/notes", response_model=ContactNoteRead)
def add_contact_note(
    contact_id: int,
    content: str,
    note_type: str = "general",
    created_by: str = None,
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """Add a note to a contact. Requires authentication."""
    contact = get_contact(db, contact_id)
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")
    note_data = ContactNoteCreate(
        contact_id=contact_id,
        content=content,
        note_type=note_type,
        created_by=created_by
    )
    return create_contact_note(db, note_data)

@router.get("/{contact_id}/notes", response_model=List[ContactNoteRead])
def get_contact_notes_endpoint(
    contact_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """List notes for a contact with pagination. Requires authentication."""
    contact = get_contact(db, contact_id)
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")
    return get_contact_notes(db, contact_id)[skip:skip+limit]

@router.delete("/notes/{note_id}", status_code=200)
def delete_note(note_id: int, db: Session = Depends(get_session), current_user=Depends(admin_required)):
    """Delete a contact note. Admin only. Returns a confirmation message."""
    success = delete_contact_note(db, note_id)
    if not success:
        raise HTTPException(status_code=404, detail="Note not found")
    return {"detail": f"Note {note_id} deleted successfully"}

@router.get("/{contact_id}/activities", response_model=List[ContactActivityRead])
def get_contact_activities_endpoint(
    contact_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """List activities for a contact with pagination. Requires authentication."""
    contact = get_contact(db, contact_id)
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")
    # Pass skip and limit to the CRUD function for DB-level pagination
    return get_contact_activities(db, contact_id, skip=skip, limit=limit)

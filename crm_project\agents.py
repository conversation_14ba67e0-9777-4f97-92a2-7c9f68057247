from pydantic_ai import Agent, RunContext
from dotenv import load_dotenv
import os
from typing import List

load_dotenv()
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY", "")

class AppDeps:
    pass

agent = Agent(
    model="openai:gpt-4o-mini",
    deps_type=AppDeps,
    system_prompt=(
        "You are a smart CRM assistant. Use tools to summarize contacts, recommend next actions,"\
        "and draft follow-up emails."
    ),
)

@agent.tool_plain
async def summarize_contact(notes: List[str]) -> str:
    return " | ".join(notes)

@agent.tool
async def recommend_next_action(ctx: RunContext[AppDeps], lead_status: str) -> str:
    mapping = {
        "new": "Send welcome email",
        "contacted": "Schedule discovery call",
        "qualified": "Prepare proposal",
        "proposal_sent": "Follow up in 3 days",
        "won": "Transfer to account manager",
        "lost": "Archive lead",
    }
    return mapping.get(lead_status.lower(), "Review lead details")

@agent.tool_plain
async def draft_email(subject: str, body_points: List[str]) -> str:
    bullets = "\n".join(f"- {pt}" for pt in body_points)
    return f"Subject: {subject}\n\nHello,\n\n{bullets}\n\nBest regards,\nCRM Bot"

import asyncio
import httpx
import hashlib
import hmac
import json
import logging
from datetime import datetime
from typing import Dict, Any
from sqlalchemy.orm import Session
from crm_project.models import Webhook
from crm_project.crud import get_webhooks, create_webhook_log, update_webhook
from crm_project.database import SessionLocal
from crm_project.enums import WebhookEvents

MAX_RETRIES = 3
RETRY_BACKOFF = 2  # seconds

class WebhookService:
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def trigger_webhooks(self, event_type: str, data: Dict[str, Any]):
        """Trigger all active webhooks for a specific event type"""
        db = SessionLocal()
        try:
            webhooks = get_webhooks(db)
            # Use Enum values for event filtering
            active_webhooks = [w for w in webhooks if w.active and event_type in w.events]
            if not active_webhooks:
                return
            payload = {
                "event_type": event_type,
                "data": data,
                "timestamp": datetime.utcnow().isoformat()
            }
            tasks = []
            for webhook in active_webhooks:
                task = asyncio.create_task(self._send_webhook_with_retries(db, webhook, payload))
                tasks.append(task)
            await asyncio.gather(*tasks, return_exceptions=True)
        finally:
            db.close()

    async def _send_webhook_with_retries(self, db: Session, webhook: Webhook, payload: Dict[str, Any]):
        for attempt in range(1, MAX_RETRIES + 1):
            try:
                await self._send_webhook(db, webhook, payload)
                return
            except Exception as e:
                logging.error(f"Webhook delivery failed (attempt {attempt}): {e}")
                if attempt < MAX_RETRIES:
                    await asyncio.sleep(RETRY_BACKOFF * attempt)
                else:
                    # Log persistent failure
                    create_webhook_log(db, webhook.id, payload["event_type"], payload, response_status=None, response_body=str(e))

    async def _send_webhook(self, db: Session, webhook: Webhook, payload: Dict[str, Any]):
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "PydanticAI-CRM-Webhook/1.0"
        }
        if webhook.secret:
            signature = self._generate_signature(webhook.secret, payload)
            headers["X-Webhook-Signature"] = signature
        response = await self.client.post(
            str(webhook.url),
            json=payload,
            headers=headers
        )
        create_webhook_log(
            db,
            webhook.id,
            payload["event_type"],
            payload,
            response_status=response.status_code,
            response_body=response.text
        )
        webhook.last_triggered = datetime.utcnow()
        db.commit()

    def _generate_signature(self, secret: str, payload: Dict[str, Any]) -> str:
        msg = json.dumps(payload, sort_keys=True).encode()
        return hmac.new(secret.encode(), msg, hashlib.sha256).hexdigest()

def trigger_webhook_event(event_type, data):
    service = WebhookService()
    asyncio.create_task(service.trigger_webhooks(event_type, data))

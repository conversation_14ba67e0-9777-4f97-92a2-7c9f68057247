<!DOCTYPE html>
<html>
<head>
    <title>Lead Pipeline - CRM</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            color: #2d3748;
            font-size: 28px;
            font-weight: 700;
        }
        .nav a {
            margin-right: 15px;
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }
        .nav a:hover { text-decoration: underline; }
        .pipeline-stats {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            flex: 1;
            text-align: center;
        }
        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #718096;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .pipeline-container {
            display: flex;
            gap: 20px;
            overflow-x: auto;
            padding-bottom: 20px;
        }
        .pipeline-stage {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            min-width: 300px;
            max-width: 300px;
            padding: 20px;
            height: fit-content;
        }
        .stage-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e2e8f0;
        }
        .stage-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
        }
        .stage-count {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .lead-card {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-left: 4px solid #667eea;
        }
        .lead-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .lead-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
            font-size: 16px;
        }
        .lead-contact {
            color: #718096;
            font-size: 14px;
            margin-bottom: 8px;
        }
        .lead-value {
            font-weight: 600;
            color: #38a169;
            font-size: 18px;
            margin-bottom: 8px;
        }
        .lead-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #a0aec0;
        }
        .probability {
            background: #edf2f7;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: background 0.2s ease;
        }
        .btn:hover {
            background: #5a67d8;
        }
        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }
        .btn-secondary:hover {
            background: #cbd5e0;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2d3748;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
        }
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #718096;
        }
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #a0aec0;
            font-style: italic;
        }
        .priority-high { border-left-color: #e53e3e; }
        .priority-medium { border-left-color: #dd6b20; }
        .priority-low { border-left-color: #38a169; }
        .priority-urgent { border-left-color: #9f7aea; }
    </style>
</head>
<body>
    <div class="header">
        <div>
            <div class="nav">
                <a href="/">← Back to Home</a>
                <a href="/contacts-enhanced">Contacts</a>
                <a href="/tasks-management">Tasks</a>
                <a href="/webhooks-ui">Webhooks</a>
            </div>
            <h1>🎯 Lead Pipeline</h1>
        </div>
        <div>
            <button class="btn" onclick="openCreateModal()">+ New Lead</button>
        </div>
    </div>

    <div class="pipeline-stats" id="pipelineStats">
        <div class="stat-card">
            <div class="stat-value" id="totalLeads">-</div>
            <div class="stat-label">Total Leads</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="totalValue">-</div>
            <div class="stat-label">Pipeline Value</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="avgProbability">-</div>
            <div class="stat-label">Avg Probability</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="closedWon">-</div>
            <div class="stat-label">Closed Won</div>
        </div>
    </div>

    <div class="pipeline-container" id="pipelineContainer">
        <div class="loading">Loading pipeline...</div>
    </div>

    <!-- Create Lead Modal -->
    <div id="createModal" class="modal">
        <div class="modal-content">
            <h2 style="margin-bottom: 20px;">Create New Lead</h2>
            <form id="createLeadForm">
                <div class="form-group">
                    <label>Lead Title *</label>
                    <input type="text" id="title" required placeholder="e.g., Website Redesign Project">
                </div>

                <div class="form-group">
                    <label>Description</label>
                    <textarea id="description" placeholder="Lead description and details..."></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Contact *</label>
                        <select id="contact_id" required>
                            <option value="">Select Contact</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Account</label>
                        <select id="account_id">
                            <option value="">Select Account</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Stage</label>
                        <select id="stage">
                            <option value="prospect">Prospect</option>
                            <option value="qualified">Qualified</option>
                            <option value="proposal">Proposal</option>
                            <option value="negotiation">Negotiation</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Priority</label>
                        <select id="priority">
                            <option value="low">Low</option>
                            <option value="medium" selected>Medium</option>
                            <option value="high">High</option>
                            <option value="urgent">Urgent</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Value ($)</label>
                        <input type="number" id="value" step="0.01" placeholder="0.00">
                    </div>
                    <div class="form-group">
                        <label>Probability (%)</label>
                        <input type="number" id="probability" min="0" max="100" placeholder="50">
                    </div>
                </div>

                <div class="form-group">
                    <label>Expected Close Date</label>
                    <input type="date" id="expected_close_date">
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" class="btn btn-secondary" onclick="closeCreateModal()">Cancel</button>
                    <button type="submit" class="btn">Create Lead</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Helper to get JWT from localStorage
        function getAuthHeaders() {
            const token = localStorage.getItem('crm_jwt');
            return token ? { 'Authorization': 'Bearer ' + token } : {};
        }

        let leads = [];
        let contacts = [];
        let accounts = [];

        const stages = [
            { name: 'prospect', title: 'Prospect', color: '#667eea' },
            { name: 'qualified', title: 'Qualified', color: '#38a169' },
            { name: 'proposal', title: 'Proposal', color: '#dd6b20' },
            { name: 'negotiation', title: 'Negotiation', color: '#9f7aea' },
            { name: 'closed_won', title: 'Closed Won', color: '#38a169' },
            { name: 'closed_lost', title: 'Closed Lost', color: '#e53e3e' }
        ];

        async function loadData() {
            await Promise.all([
                loadLeads(),
                loadContacts(),
                loadAccounts()
            ]);
            renderPipeline();
            updateStats();
        }

        async function loadLeads() {
            try {
                const response = await fetch('/leads/', {
                    headers: getAuthHeaders()
                });
                if (response.ok) {
                    leads = await response.json();
                } else {
                    console.error('Failed to load leads');
                    leads = [];
                }
            } catch (error) {
                console.error('Error loading leads:', error);
                leads = [];
            }
        }

        async function loadContacts() {
            try {
                const response = await fetch('/contacts/', {
                    headers: getAuthHeaders()
                });
                if (response.ok) {
                    contacts = await response.json();
                    populateContactSelect();
                } else {
                    console.error('Failed to load contacts');
                }
            } catch (error) {
                console.error('Error loading contacts:', error);
            }
        }

        async function loadAccounts() {
            try {
                const response = await fetch('/accounts/', {
                    headers: getAuthHeaders()
                });
                if (response.ok) {
                    accounts = await response.json();
                    populateAccountSelect();
                } else {
                    console.error('Failed to load accounts');
                }
            } catch (error) {
                console.error('Error loading accounts:', error);
            }
        }

        function populateContactSelect() {
            const select = document.getElementById('contact_id');
            select.innerHTML = '<option value="">Select Contact</option>';
            contacts.forEach(contact => {
                const option = document.createElement('option');
                option.value = contact.id;
                option.textContent = `${contact.first_name} ${contact.last_name}`;
                select.appendChild(option);
            });
        }

        function populateAccountSelect() {
            const select = document.getElementById('account_id');
            select.innerHTML = '<option value="">Select Account</option>';
            accounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.id;
                option.textContent = account.name;
                select.appendChild(option);
            });
        }

        function renderPipeline() {
            const container = document.getElementById('pipelineContainer');
            container.innerHTML = '';

            stages.forEach(stage => {
                const stageLeads = leads.filter(lead => lead.stage === stage.name);
                const stageElement = createStageElement(stage, stageLeads);
                container.appendChild(stageElement);
            });
        }

        function createStageElement(stage, stageLeads) {
            const stageDiv = document.createElement('div');
            stageDiv.className = 'pipeline-stage';
            
            stageDiv.innerHTML = `
                <div class="stage-header">
                    <div class="stage-title">${stage.title}</div>
                    <div class="stage-count">${stageLeads.length}</div>
                </div>
                <div class="stage-leads">
                    ${stageLeads.length === 0 ? 
                        '<div class="empty-state">No leads in this stage</div>' :
                        stageLeads.map(lead => createLeadCard(lead)).join('')
                    }
                </div>
            `;

            return stageDiv;
        }

        function createLeadCard(lead) {
            const contact = contacts.find(c => c.id === lead.contact_id);
            const contactName = contact ? `${contact.first_name} ${contact.last_name}` : 'Unknown Contact';
            
            return `
                <div class="lead-card priority-${lead.priority}" onclick="editLead(${lead.id})">
                    <div class="lead-title">${lead.title}</div>
                    <div class="lead-contact">${contactName}</div>
                    <div class="lead-value">$${(lead.value || 0).toLocaleString()}</div>
                    <div class="lead-meta">
                        <span>${lead.priority}</span>
                        <span class="probability">${lead.probability || 0}%</span>
                    </div>
                </div>
            `;
        }

        function updateStats() {
            const totalLeads = leads.length;
            const totalValue = leads.reduce((sum, lead) => sum + (lead.value || 0), 0);
            const avgProbability = totalLeads > 0 ? 
                Math.round(leads.reduce((sum, lead) => sum + (lead.probability || 0), 0) / totalLeads) : 0;
            const closedWon = leads.filter(lead => lead.stage === 'closed_won').length;

            document.getElementById('totalLeads').textContent = totalLeads;
            document.getElementById('totalValue').textContent = '$' + totalValue.toLocaleString();
            document.getElementById('avgProbability').textContent = avgProbability + '%';
            document.getElementById('closedWon').textContent = closedWon;
        }

        function openCreateModal() {
            document.getElementById('createModal').style.display = 'block';
        }

        function closeCreateModal() {
            document.getElementById('createModal').style.display = 'none';
            document.getElementById('createLeadForm').reset();
        }

        function editLead(leadId) {
            // TODO: Implement lead editing
            console.log('Edit lead:', leadId);
        }

        // Handle form submission
        document.getElementById('createLeadForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const leadData = {
                title: document.getElementById('title').value,
                description: document.getElementById('description').value || null,
                contact_id: parseInt(document.getElementById('contact_id').value),
                account_id: document.getElementById('account_id').value ? parseInt(document.getElementById('account_id').value) : null,
                stage: document.getElementById('stage').value,
                priority: document.getElementById('priority').value,
                value: document.getElementById('value').value ? parseFloat(document.getElementById('value').value) : null,
                probability: document.getElementById('probability').value ? parseInt(document.getElementById('probability').value) : null,
                expected_close_date: document.getElementById('expected_close_date').value || null
            };

            try {
                const response = await fetch('/leads/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    body: JSON.stringify(leadData)
                });

                if (response.ok) {
                    closeCreateModal();
                    await loadLeads();
                    renderPipeline();
                    updateStats();
                } else {
                    const error = await response.json();
                    alert('Error creating lead: ' + (error.detail || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error creating lead:', error);
                alert('Error creating lead');
            }
        });

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('createModal');
            if (event.target === modal) {
                closeCreateModal();
            }
        }

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>

# PydanticAI CRM (SQLAlchemy)

A CRM built with FastAPI, SQLAlchemy ORM, and Pydantic AI for smart features (email drafting, lead recommendations, summaries).

## Setup

1. Clone & install:
   ```bash
   git clone <repo-url> crm_project
   cd crm_project
   python -m venv .venv
   .venv\Scripts\activate  # Windows
   pip install -r requirements.txt
   ```
2. Copy & edit env:
   ```bash
   cp .env.example .env
   ```
3. Initialize database:
   ```bash
   python -c "from database import init_db; init_db()"
   ```
4. Run server:
   ```bash
   uvicorn main:app --reload
   ```

Browse API docs at `http://127.0.0.1:8000/docs`.

import os
from datetime import datetime, timedelta
from jose import J<PERSON><PERSON><PERSON><PERSON>, jwt
from typing import Optional
from fastapi import HTTPException, status, Depends
from fastapi.security import OAuth2<PERSON>asswordBearer
from sqlalchemy.orm import Session
from crm_project.database import get_session
from crm_project.crud import get_user_by_username

# Secret key and algorithm
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "supersecretkey")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/login")

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    expire = datetime.utcnow() + (expires_delta or timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_access_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        role: str = payload.get("role")
        if username is None or role is None:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
        return {"username": username, "role": role}
    except JWTError:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")

def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_session)):
    creds = verify_access_token(token)
    user = get_user_by_username(db, creds["username"])
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

def get_current_username(token: str = Depends(oauth2_scheme)):
    creds = verify_access_token(token)
    return creds["username"]

def admin_required(current_user = Depends(get_current_user)):
    """
    Dependency to enforce admin-only access.
    If the environment variable DISABLE_ADMIN_LOCK_FOR_TESTS is set to "1",
    this check is bypassed (for integration testing convenience).
    Otherwise, only users with role 'admin' are allowed.
    """
    if os.getenv("DISABLE_ADMIN_LOCK_FOR_TESTS") == "1":
        return current_user
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Admin privileges required")
    return current_user

# Utility to ensure test users exist in the test database
from crm_project.crud import get_user_by_username, create_user
from crm_project.database import SessionLocal

def ensure_test_users():
    db = SessionLocal()
    try:
        if not get_user_by_username(db, "admin"):
            create_user(db, username="admin", email="<EMAIL>", password="admin123", role="admin")
        if not get_user_by_username(db, "user1"):
            create_user(db, username="user1", email="<EMAIL>", password="user123", role="user")
        if not get_user_by_username(db, "user2"):
            create_user(db, username="user2", email="<EMAIL>", password="user123", role="user")
    finally:
        db.close()

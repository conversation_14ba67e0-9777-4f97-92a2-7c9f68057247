import pytest
from fastapi.testclient import TestClient
from crm_project.main import app
from crm_project.models import User, Webhook
from crm_project.database import get_session
from sqlalchemy.orm import Session
from crm_project.test_utils import ensure_test_users

ensure_test_users()

def auth_header(username="admin", password="admin123"):
    client = TestClient(app)
    resp = client.post("/auth/login", data={"username": username, "password": password})
    token = resp.json().get("access_token")
    assert token, f"<PERSON><PERSON> failed for {username}: {resp.text}"
    return {"Authorization": f"Bearer {token}"}

client = TestClient(app)

def setup_admin():
    db: Session = next(get_session())
    from crm_project.crud import create_user, get_user_by_username, pwd_context
    admin = get_user_by_username(db, "admin")
    if not admin:
        admin = create_user(db, username="admin", email="<EMAIL>", password="admin123", role="admin")
    db.close()

setup_admin()

# Utility to clean up webhooks by name or url after each test

def cleanup_webhooks(db: Session, name: str = None, url: str = None):
    query = db.query(Webhook)
    if name:
        query = query.filter(Webhook.name == name)
    if url:
        query = query.filter(Webhook.url == url)
    deleted = query.delete(synchronize_session=False)
    db.commit()
    return deleted

def test_create_webhook():
    payload = {
        "name": "Test Webhook",
        "url": "https://webhook.site/unique-url",
        "events": ["lead.created"],
        "secret": "supersecret",
        "active": True
    }
    try:
        response = client.post("/webhooks/", json=payload, headers=auth_header())
        assert response.status_code == 200 or response.status_code == 201
        data = response.json()
        assert data["name"] == "Test Webhook"
        assert data["url"] == payload["url"]
        assert data["secret"] is None
    finally:
        db = next(get_session())
        cleanup_webhooks(db, name="Test Webhook", url="https://webhook.site/unique-url")
        db.close()

def test_duplicate_webhook():
    payload = {
        "name": "Dup Webhook",
        "url": "https://webhook.site/dup-url",
        "events": ["lead.created"],
        "secret": "supersecret",
        "active": True
    }
    try:
        client.post("/webhooks/", json=payload, headers=auth_header())
        response = client.post("/webhooks/", json=payload, headers=auth_header())
        assert response.status_code == 400 or response.status_code == 422
        assert "Duplicate" in response.text or "duplicate" in response.text
    finally:
        db = next(get_session())
        cleanup_webhooks(db, name="Dup Webhook", url="https://webhook.site/dup-url")
        db.close()

def test_webhook_ssrf_protection():
    payload = {
        "name": "Bad Webhook",
        "url": "http://localhost:8000/evil",
        "events": ["lead.created"],
        "secret": "supersecret",
        "active": True
    }
    try:
        response = client.post("/webhooks/", json=payload, headers=auth_header())
        assert response.status_code == 400 or response.status_code == 422
        assert "localhost" in response.text or "private" in response.text
    finally:
        db = next(get_session())
        cleanup_webhooks(db, name="Bad Webhook", url="http://localhost:8000/evil")
        db.close()

def test_list_webhooks_pagination():
    names = []
    try:
        for i in range(5):
            name = f"Paginate Webhook {i}"
            names.append(name)
            payload = {
                "name": name,
                "url": f"https://webhook.site/paginate-{i}",
                "events": ["lead.created"],
                "active": True
            }
            client.post("/webhooks/", json=payload, headers=auth_header())
        response = client.get("/webhooks/?skip=0&limit=3", headers=auth_header())
        assert response.status_code == 200
        assert len(response.json()) <= 3
    finally:
        db = next(get_session())
        for i in range(5):
            cleanup_webhooks(db, name=f"Paginate Webhook {i}", url=f"https://webhook.site/paginate-{i}")
        db.close()

def test_webhook_admin_required():
    payload = {
        "name": "User Webhook",
        "url": "https://webhook.site/user-url",
        "events": ["lead.created"],
        "active": True
    }
    try:
        from crm_project.crud import create_user, get_user_by_username
        db: Session = next(get_session())
        user = get_user_by_username(db, "user")
        if not user:
            user = create_user(db, username="user", email="<EMAIL>", password="userpass", role="user")
        db.close()
        user_header = auth_header(username="user", password="userpass")
        response = client.post("/webhooks/", json=payload, headers=user_header)
        assert response.status_code == 403
    finally:
        db = next(get_session())
        cleanup_webhooks(db, name="User Webhook", url="https://webhook.site/user-url")
        db.close()

def test_webhook_secret_not_exposed():
    payload = {
        "name": "Secret Webhook",
        "url": "https://webhook.site/secret-url",
        "events": ["lead.created"],
        "secret": "shouldnotsee",
        "active": True
    }
    try:
        response = client.post("/webhooks/", json=payload, headers=auth_header())
        assert response.status_code == 200 or response.status_code == 201
        data = response.json()
        assert "secret" in data and data["secret"] is None
    finally:
        db = next(get_session())
        cleanup_webhooks(db, name="Secret Webhook", url="https://webhook.site/secret-url")
        db.close()

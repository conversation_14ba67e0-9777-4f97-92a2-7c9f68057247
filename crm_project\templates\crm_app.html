<!DOCTYPE html>
<html>
<head>
    <title>Simple CRM Web App</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <style>
        body {
            font-family: 'Inter', '<PERSON><PERSON>', Arial, sans-serif;
            background: #f7f9fb;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .nav a {
            margin-right: 15px;
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }
        .nav a:hover {
            text-decoration: underline;
        }
        h1, h2 {
            color: #2d3748;
            margin: 0;
        }
        .form-section {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
        }
        .form-group input {
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        .btn:hover {
            background: #0056b3;
        }
        .table-section {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2d3748;
        }
        .delete-btn {
            background: #dc3545;
            color: white;
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .delete-btn:hover {
            background: #c82333;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #718096;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div>
                <div class="nav">
                    <a href="/">← Back to Home</a>
                    <a href="/contacts-enhanced">Enhanced Contacts</a>
                    <a href="/leads-pipeline">Leads</a>
                    <a href="/tasks-management">Tasks</a>
                    <a href="/webhooks-ui">Webhooks</a>
                </div>
                <h1>👥 Simple CRM</h1>
            </div>
        </div>

        <div class="form-section">
            <h2>Add New Contact</h2>
            <form id="addContactForm">
                <div class="form-grid">
                    <div class="form-group">
                        <input type="text" id="first_name" placeholder="First Name" required>
                    </div>
                    <div class="form-group">
                        <input type="text" id="last_name" placeholder="Last Name" required>
                    </div>
                    <div class="form-group">
                        <input type="email" id="email" placeholder="Email" required>
                    </div>
                    <div class="form-group">
                        <input type="text" id="phone" placeholder="Phone">
                    </div>
                </div>
                <button type="submit" class="btn">Add Contact</button>
            </form>
        </div>

        <div class="table-section">
            <h2>Contacts</h2>
            <table id="contactsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="6" class="loading">Loading contacts...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script src="/static/js/crm_app.js"></script>
</body>
</html>

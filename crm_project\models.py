from sqlalchemy import Column, Integer, String, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ey, Text, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
from crm_project.database import Base

class Contact(Base):
    __tablename__ = "contacts"
    id = Column(Integer, primary_key=True, index=True)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    phone = Column(String, nullable=True)

    # Enhanced contact fields
    job_title = Column(String, nullable=True)
    department = Column(String, nullable=True)
    account_id = Column(Integer, ForeignKey("accounts.id"), nullable=True)
    tags = Column(JSON, default=list)  # List of tags for categorization
    custom_fields = Column(JSON, default=dict)  # Flexible custom data

    # Social and additional contact info
    linkedin_url = Column(String, nullable=True)
    twitter_handle = Column(String, nullable=True)
    address = Column(Text, nullable=True)

    # Status and lifecycle
    status = Column(String, default="active")  # active, inactive, prospect, customer
    lead_source = Column(String, nullable=True)  # How they were acquired

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    account = relationship("Account", back_populates="contacts")
    leads = relationship("Lead", back_populates="contact")
    tasks = relationship("Task", back_populates="contact")
    notes = relationship("ContactNote", back_populates="contact", cascade="all, delete-orphan")
    activities = relationship("ContactActivity", back_populates="contact", cascade="all, delete-orphan")

class Account(Base):
    __tablename__ = "accounts"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    industry = Column(String, nullable=True)
    website = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    contacts = relationship("Contact", back_populates="account")
    leads = relationship("Lead", back_populates="account")
    tasks = relationship("Task", back_populates="account", cascade="all, delete-orphan")

class Lead(Base):
    __tablename__ = "leads"
    id = Column(Integer, primary_key=True, index=True)

    # Basic lead information
    title = Column(String, nullable=False)  # Deal/opportunity title
    description = Column(Text, nullable=True)  # Deal description
    contact_id = Column(Integer, ForeignKey("contacts.id"), nullable=False)
    account_id = Column(Integer, ForeignKey("accounts.id"), nullable=True)

    # Pipeline and sales tracking
    stage = Column(String, default="prospect", index=True)  # prospect, qualified, proposal, negotiation, closed_won, closed_lost
    probability = Column(Integer, default=10)  # Probability of closing (0-100%)
    value = Column(Integer, default=0)  # Deal value in cents
    expected_close_date = Column(DateTime, nullable=True)

    # Lead source and tracking
    source = Column(String, nullable=True)  # website, referral, cold_call, etc.
    campaign = Column(String, nullable=True)  # Marketing campaign

    # Status and lifecycle
    status = Column(String, default="active")  # active, inactive, archived
    priority = Column(String, default="medium")  # low, medium, high, urgent

    # Additional tracking
    tags = Column(JSON, default=list)  # List of tags for categorization
    custom_fields = Column(JSON, default=dict)  # Flexible custom data

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    closed_at = Column(DateTime, nullable=True)

    # Relationships
    contact = relationship("Contact", back_populates="leads")
    account = relationship("Account", back_populates="leads")
    tasks = relationship("Task", back_populates="lead", cascade="all, delete-orphan")
    activities = relationship("LeadActivity", back_populates="lead", cascade="all, delete-orphan")
    notes = relationship("LeadNote", back_populates="lead", cascade="all, delete-orphan")

class Task(Base):
    __tablename__ = "tasks"
    id = Column(Integer, primary_key=True, index=True)

    # Core task information
    title = Column(String, nullable=False)  # Task title/summary
    description = Column(Text, nullable=True)  # Detailed description
    task_type = Column(String, default="general")  # call, email, meeting, follow_up, demo, proposal, etc.

    # Priority and status
    priority = Column(String, default="medium")  # low, medium, high, urgent
    status = Column(String, default="pending")  # pending, in_progress, completed, cancelled

    # Scheduling
    due_date = Column(DateTime, nullable=True)
    start_date = Column(DateTime, nullable=True)
    estimated_duration = Column(Integer, nullable=True)  # Duration in minutes

    # Relationships - can be linked to contact, lead, or account
    contact_id = Column(Integer, ForeignKey("contacts.id"), nullable=True)
    lead_id = Column(Integer, ForeignKey("leads.id"), nullable=True)
    account_id = Column(Integer, ForeignKey("accounts.id"), nullable=True)

    # Additional tracking
    tags = Column(JSON, default=list)  # List of tags for categorization
    notes = Column(Text, nullable=True)  # Internal notes about the task

    # Completion tracking
    completed = Column(Boolean, default=False)
    completed_at = Column(DateTime, nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    contact = relationship("Contact", back_populates="tasks")
    lead = relationship("Lead", back_populates="tasks")
    account = relationship("Account", back_populates="tasks")

class Webhook(Base):
    __tablename__ = "webhooks"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    url = Column(String, nullable=False)
    events = Column(JSON, nullable=False)  # List of events to listen for
    secret = Column(String, nullable=True)  # Secret for webhook verification
    active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_triggered = Column(DateTime, nullable=True)

class WebhookLog(Base):
    __tablename__ = "webhook_logs"
    id = Column(Integer, primary_key=True, index=True)
    webhook_id = Column(Integer, ForeignKey("webhooks.id"))
    event_type = Column(String, nullable=False)
    payload = Column(JSON, nullable=False)
    response_status = Column(Integer, nullable=True)
    response_body = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

class ContactNote(Base):
    __tablename__ = "contact_notes"
    id = Column(Integer, primary_key=True, index=True)
    contact_id = Column(Integer, ForeignKey("contacts.id"), nullable=False)
    content = Column(Text, nullable=False)
    note_type = Column(String, default="general")  # general, call, meeting, email, etc.
    created_by = Column(String, nullable=True)  # User who created the note
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    contact = relationship("Contact", back_populates="notes")

class ContactActivity(Base):
    __tablename__ = "contact_activities"
    id = Column(Integer, primary_key=True, index=True)
    contact_id = Column(Integer, ForeignKey("contacts.id"), nullable=False)
    activity_type = Column(String, nullable=False)  # created, updated, email_sent, call_made, etc.
    description = Column(Text, nullable=False)
    activity_data = Column(JSON, default=dict)  # Additional activity data
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    contact = relationship("Contact", back_populates="activities")

class LeadNote(Base):
    __tablename__ = "lead_notes"
    id = Column(Integer, primary_key=True, index=True)
    lead_id = Column(Integer, ForeignKey("leads.id"), nullable=False)
    content = Column(Text, nullable=False)
    note_type = Column(String, default="general")  # general, call, meeting, proposal, etc.
    created_by = Column(String, nullable=True)  # User who created the note
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    lead = relationship("Lead", back_populates="notes")

class LeadActivity(Base):
    __tablename__ = "lead_activities"
    id = Column(Integer, primary_key=True, index=True)
    lead_id = Column(Integer, ForeignKey("leads.id"), nullable=False)
    activity_type = Column(String, nullable=False)  # stage_changed, value_updated, note_added, etc.
    description = Column(Text, nullable=False)
    activity_data = Column(JSON, default=dict)  # Additional activity data
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    lead = relationship("Lead", back_populates="activities")

class Setting(Base):
    __tablename__ = "settings"
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String, unique=True, nullable=False, index=True)
    value = Column(String, nullable=True)

class User(Base):
    __tablename__ = "users"
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, nullable=False, index=True)
    email = Column(String, unique=True, nullable=False, index=True)
    password_hash = Column(String, nullable=False)
    role = Column(String, default="user")  # 'admin' or 'user'
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

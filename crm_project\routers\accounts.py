from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from crm_project.database import get_session
from crm_project.crud import create_account, get_accounts, get_account, update_account, delete_account
from crm_project.schemas import Account<PERSON>reate, AccountRead
from crm_project.jwt_utils import get_current_user
from crm_project.models import Account

router = APIRouter(prefix="/accounts", tags=["Accounts"])

@router.post("/", response_model=AccountRead, status_code=status.HTTP_201_CREATED)
def add_account(payload: AccountCreate, db: Session = Depends(get_session), current_user=Depends(get_current_user)):
    """Create a new account. Requires authentication. Handles duplicate names."""
    existing = db.query(Account).filter_by(name=payload.name).first()
    if existing:
        raise HTTPException(status_code=400, detail="Account with this name already exists")
    return create_account(db, payload)

@router.get("/", response_model=list[AccountRead])
def list_accounts(
    db: Session = Depends(get_session),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user=Depends(get_current_user)
):
    """List accounts with pagination. Requires authentication."""
    return db.query(Account).offset(skip).limit(limit).all()

@router.get("/{account_id}", response_model=AccountRead)
def get_one(account_id: int, db: Session = Depends(get_session), current_user=Depends(get_current_user)):
    """Get account by ID. Requires authentication."""
    account = get_account(db, account_id)
    if not account:
        raise HTTPException(status_code=404, detail="Account not found")
    return account

@router.put("/{account_id}", response_model=AccountRead)
def update_one(account_id: int, payload: AccountCreate, db: Session = Depends(get_session), current_user=Depends(get_current_user)):
    """Update an account. Requires authentication. Handles duplicate names."""
    # Check for duplicate name (excluding self)
    existing = db.query(Account).filter(Account.name == payload.name, Account.id != account_id).first()
    if existing:
        raise HTTPException(status_code=400, detail="Account with this name already exists")
    updated = update_account(db, account_id, payload)
    if not updated:
        raise HTTPException(status_code=404, detail="Account not found")
    return updated

@router.delete("/{account_id}", status_code=200)
def delete_one(account_id: int, db: Session = Depends(get_session)):
    """Delete an account. Returns a confirmation message."""
    deleted = delete_account(db, account_id)
    if not deleted:
        raise HTTPException(status_code=404, detail="Account not found")
    return {"detail": f"Account {account_id} deleted successfully"}

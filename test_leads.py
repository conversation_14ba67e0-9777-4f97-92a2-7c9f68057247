import pytest
from fastapi.testclient import TestClient
from crm_project.main import app
from crm_project.database import get_session
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from crm_project.database import Base
from crm_project.test_utils import ensure_test_users

SQLALCHEMY_DATABASE_URL = "sqlite:///./test_crm.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base.metadata.create_all(bind=engine)

def override_get_session():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_session] = override_get_session
client = TestClient(app)

ensure_test_users()

def get_jwt_token():
    client.post("/auth/register", json={"username": "leaduser", "email": "<EMAIL>", "password": "testpass"})
    resp = client.post("/auth/login", data={"username": "leaduser", "password": "testpass"})
    assert resp.status_code == 200, resp.text
    return resp.json()["access_token"]

def auth_header():
    return {"Authorization": f"Bearer {get_jwt_token()}"}

def test_create_lead():
    # LeadCreate requires: title, contact_id, stage, probability, value (see LeadBase)
    # We'll create a contact first (simulate, as no endpoint for contacts in this test)
    from crm_project.models import Contact
    db = next(override_get_session())
    contact = db.query(Contact).filter_by(email="<EMAIL>").first()
    if not contact:
        contact = Contact(first_name="Lead", last_name="Contact", email="<EMAIL>", phone="1234567899")
        db.add(contact)
        db.commit()
        db.refresh(contact)
    db.close()
    response = client.post("/leads/", json={"title": "Test Lead", "contact_id": contact.id, "stage": "prospect", "probability": 10, "value": 1000}, headers=auth_header())
    assert response.status_code in (201, 400)

def test_list_leads_pagination():
    from crm_project.models import Contact
    db = next(override_get_session())
    contact = db.query(Contact).filter_by(email="<EMAIL>").first()
    if not contact:
        contact = Contact(first_name="Lead", last_name="Contact", email="<EMAIL>", phone="1234567899")
        db.add(contact)
        db.commit()
        db.refresh(contact)
    db.close()
    for i in range(10):
        client.post("/leads/", json={"title": f"Lead {i}", "contact_id": contact.id, "stage": "prospect", "probability": 10, "value": 1000+i}, headers=auth_header())
    resp = client.get("/leads/?skip=0&limit=5", headers=auth_header())
    assert resp.status_code == 200
    data = resp.json()
    assert isinstance(data, list)
    assert len(data) <= 5
    resp2 = client.get("/leads/?skip=5&limit=5", headers=auth_header())
    assert resp2.status_code == 200
    data2 = resp2.json()
    assert isinstance(data2, list)
    assert len(data2) <= 5

def test_get_lead_detail():
    from crm_project.models import Contact
    db = next(override_get_session())
    contact = db.query(Contact).filter_by(email="<EMAIL>").first()
    if not contact:
        contact = Contact(first_name="Lead", last_name="Contact", email="<EMAIL>", phone="1234567899")
        db.add(contact)
        db.commit()
        db.refresh(contact)
    db.close()
    create_resp = client.post("/leads/", json={"title": "Detail Lead", "contact_id": contact.id, "stage": "prospect", "probability": 10, "value": 2000}, headers=auth_header())
    if create_resp.status_code == 400:
        list_resp = client.get("/leads/", headers=auth_header())
        lead = next((a for a in list_resp.json() if a["title"] == "Detail Lead"), None)
        lead_id = lead["id"] if lead else 1
    else:
        lead_id = create_resp.json()["id"]
    resp = client.get(f"/leads/{lead_id}", headers=auth_header())
    assert resp.status_code == 200

def test_update_lead():
    from crm_project.models import Contact
    db = next(override_get_session())
    contact = db.query(Contact).filter_by(email="<EMAIL>").first()
    if not contact:
        contact = Contact(first_name="Lead", last_name="Contact", email="<EMAIL>", phone="1234567899")
        db.add(contact)
        db.commit()
        db.refresh(contact)
    db.close()
    create_resp = client.post("/leads/", json={"title": "Update Lead", "contact_id": contact.id, "stage": "prospect", "probability": 10, "value": 3000}, headers=auth_header())
    if create_resp.status_code == 400:
        list_resp = client.get("/leads/", headers=auth_header())
        lead = next((a for a in list_resp.json() if a["title"] == "Update Lead"), None)
        lead_id = lead["id"] if lead else 1
    else:
        lead_id = create_resp.json()["id"]
    resp = client.put(f"/leads/{lead_id}", json={"title": "Updated Lead", "contact_id": contact.id, "stage": "qualified", "probability": 70, "value": 3500}, headers=auth_header())
    assert resp.status_code in (200, 404, 400)

def test_delete_lead():
    from crm_project.models import Contact
    db = next(override_get_session())
    contact = db.query(Contact).filter_by(email="<EMAIL>").first()
    if not contact:
        contact = Contact(first_name="Lead", last_name="Contact", email="<EMAIL>", phone="1234567899")
        db.add(contact)
        db.commit()
        db.refresh(contact)
    db.close()
    create_resp = client.post("/leads/", json={"title": "Delete Lead", "contact_id": contact.id, "stage": "prospect", "probability": 10, "value": 4000}, headers=auth_header())
    if create_resp.status_code == 400:
        list_resp = client.get("/leads/", headers=auth_header())
        lead = next((a for a in list_resp.json() if a["title"] == "Delete Lead"), None)
        lead_id = lead["id"] if lead else 1
    else:
        lead_id = create_resp.json()["id"]
    # Try as normal user (should fail with 403)
    resp = client.delete(f"/leads/{lead_id}", headers=auth_header())
    assert resp.status_code in (403, 404)

def test_lead_notes_pagination():
    from crm_project.models import Contact
    db = next(override_get_session())
    contact = db.query(Contact).filter_by(email="<EMAIL>").first()
    if not contact:
        contact = Contact(first_name="Lead", last_name="Contact", email="<EMAIL>", phone="1234567899")
        db.add(contact)
        db.commit()
        db.refresh(contact)
    db.close()
    create_resp = client.post("/leads/", json={"title": "Note Lead", "contact_id": contact.id, "stage": "prospect", "probability": 10, "value": 5000}, headers=auth_header())
    if create_resp.status_code == 400:
        list_resp = client.get("/leads/", headers=auth_header())
        lead = next((a for a in list_resp.json() if a["title"] == "Note Lead"), None)
        lead_id = lead["id"] if lead else 1
    else:
        lead_id = create_resp.json()["id"]
    # Add notes
    for i in range(7):
        client.post(f"/leads/{lead_id}/notes", json={"content": f"Note {i}", "note_type": "general"}, headers=auth_header())
    resp = client.get(f"/leads/{lead_id}/notes?skip=0&limit=3", headers=auth_header())
    assert resp.status_code == 200
    data = resp.json()
    assert isinstance(data, list)
    assert len(data) <= 3
    resp2 = client.get(f"/leads/{lead_id}/notes?skip=3&limit=3", headers=auth_header())
    assert resp2.status_code == 200
    data2 = resp2.json()
    assert isinstance(data2, list)
    assert len(data2) <= 3
    # Test delete note as normal user (should fail with 403)
    if data:
        note_id = data[0]["id"]
        resp3 = client.delete(f"/leads/notes/{note_id}", headers=auth_header())
        assert resp3.status_code in (403, 404)

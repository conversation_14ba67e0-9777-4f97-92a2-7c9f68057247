import pytest
from fastapi.testclient import TestClient
from crm_project.main import app
from crm_project.database import get_session
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from crm_project.database import Base
import os
from crm_project.test_utils import ensure_test_users

# Ensure test users exist
ensure_test_users()

# Setup test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_crm.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base.metadata.create_all(bind=engine)

def override_get_session():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_session] = override_get_session
client = TestClient(app)

def get_jwt_token():
    # Register user (ignore if already exists)
    client.post("/auth/register", json={"username": "testuser", "email": "<EMAIL>", "password": "testpass"})
    # Login user
    resp = client.post("/auth/login", data={"username": "testuser", "password": "testpass"})
    assert resp.status_code == 200, resp.text
    return resp.json()["access_token"]

def auth_header():
    return {"Authorization": f"Bearer {get_jwt_token()}"}

def test_create_account():
    response = client.post("/accounts/", json={"name": "Test Account", "industry": "IT", "website": "https://test.com"}, headers=auth_header())
    assert response.status_code in (200, 201, 400)  # 400 if duplicate

def test_list_accounts():
    response = client.get("/accounts/", headers=auth_header())
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_get_account():
    # Create first
    create_resp = client.post("/accounts/", json={"name": "GetTest", "industry": "IT", "website": "https://get.com"}, headers=auth_header())
    if create_resp.status_code == 400:
        # Already exists, try to get by name
        list_resp = client.get("/accounts/", headers=auth_header())
        acc = next((a for a in list_resp.json() if a["name"] == "GetTest"), None)
        acc_id = acc["id"] if acc else 1
    else:
        acc_id = create_resp.json()["id"]
    response = client.get(f"/accounts/{acc_id}", headers=auth_header())
    assert response.status_code == 200

def test_update_account():
    create_resp = client.post("/accounts/", json={"name": "UpdateTest", "industry": "IT", "website": "https://update.com"}, headers=auth_header())
    if create_resp.status_code == 400:
        list_resp = client.get("/accounts/", headers=auth_header())
        acc = next((a for a in list_resp.json() if a["name"] == "UpdateTest"), None)
        acc_id = acc["id"] if acc else 1
    else:
        acc_id = create_resp.json()["id"]
    # Try updating to a unique name (should succeed or 404)
    response = client.put(f"/accounts/{acc_id}", json={"name": "Updated Name", "industry": "Finance", "website": "https://updated.com"}, headers=auth_header())
    assert response.status_code in (200, 404, 400)  # 400 allowed for duplicate name
    # Now try updating to a duplicate name (should get 400)
    client.post("/accounts/", json={"name": "DupName", "industry": "IT", "website": "https://dup.com"}, headers=auth_header())
    response_dup = client.put(f"/accounts/{acc_id}", json={"name": "DupName", "industry": "Finance", "website": "https://dup.com"}, headers=auth_header())
    assert response_dup.status_code == 400

def test_delete_account():
    create_resp = client.post("/accounts/", json={"name": "DeleteTest", "industry": "IT", "website": "https://delete.com"}, headers=auth_header())
    if create_resp.status_code == 400:
        list_resp = client.get("/accounts/", headers=auth_header())
        acc = next((a for a in list_resp.json() if a["name"] == "DeleteTest"), None)
        acc_id = acc["id"] if acc else 1
    else:
        acc_id = create_resp.json()["id"]
    # Try as normal user (should fail with 403)
    response = client.delete(f"/accounts/{acc_id}", headers=auth_header())
    assert response.status_code in (403, 404)

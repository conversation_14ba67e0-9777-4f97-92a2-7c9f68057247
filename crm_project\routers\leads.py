from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from crm_project.database import get_session
from crm_project.crud import (
    create_lead, get_leads, get_lead, update_lead, delete_lead,
    get_leads_by_stage, get_pipeline_summary,
    create_lead_note, get_lead_notes, delete_lead_note
)
from crm_project.schemas import (
    LeadCreate, LeadRead, LeadUpdate, LeadDetailRead,
    LeadNoteCreate, LeadNoteRead
)
from crm_project.jwt_utils import get_current_user
from typing import Dict, Any, List
from crm_project.models import Lead  # Import Lead model to fix NameError

router = APIRouter(prefix="/leads", tags=["Leads"])

@router.post("/", response_model=LeadRead, status_code=201)
async def add_lead(
    payload: LeadCreate,
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """Create a new lead. Requires authentication. Handles duplicate checks."""
    # Duplicate check: title + contact_id should be unique for a business lead
    if db.query(Lead).filter(Lead.title == payload.title, Lead.contact_id == payload.contact_id).first():
        raise HTTPException(status_code=400, detail="Lead with this title and contact already exists")
    return create_lead(db, payload)

@router.get("/", response_model=List[LeadRead])
def list_leads(
    db: Session = Depends(get_session),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user=Depends(get_current_user)
):
    """List leads with pagination. Requires authentication."""
    return get_leads(db, skip=skip, limit=limit)

@router.get("/{lead_id}", response_model=LeadDetailRead)
def get_lead_detail(
    lead_id: int,
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """Get lead by ID. Requires authentication."""
    lead = get_lead(db, lead_id)
    if not lead:
        raise HTTPException(status_code=404, detail="Lead not found")
    result = LeadDetailRead.from_orm(lead)
    if hasattr(lead, 'contact') and lead.contact:
        result.contact_name = f"{lead.contact.first_name} {lead.contact.last_name}"
    if hasattr(lead, 'account') and lead.account:
        result.account_name = lead.account.name
    return result

@router.put("/{lead_id}", response_model=LeadRead)
async def update_lead_endpoint(
    lead_id: int,
    payload: LeadUpdate,
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """Update a lead. Requires authentication. Handles duplicate checks."""
    # Duplicate check: title + contact_id should be unique (excluding self)
    if payload.title and payload.contact_id:
        if db.query(Lead).filter(Lead.title == payload.title, Lead.contact_id == payload.contact_id, Lead.id != lead_id).first():
            raise HTTPException(status_code=400, detail="Lead with this title and contact already exists")
    lead = update_lead(db, lead_id, payload)
    if not lead:
        raise HTTPException(status_code=404, detail="Lead not found")
    return lead

@router.delete("/{lead_id}", status_code=200)
async def delete_lead_endpoint(
    lead_id: int,
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """Delete a lead. Admin only. Returns a confirmation message."""
    if not delete_lead(db, lead_id):
        raise HTTPException(status_code=404, detail="Lead not found")
    return {"detail": f"Lead {lead_id} deleted successfully"}

@router.get("/stage/{stage}", response_model=List[LeadRead])
def get_leads_by_stage_endpoint(
    stage: str,
    db: Session = Depends(get_session),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user=Depends(get_current_user)
):
    """List leads by stage with pagination. Requires authentication."""
    return get_leads_by_stage(db, stage, skip=skip, limit=limit)

@router.get("/pipeline/summary")
def get_pipeline_summary_endpoint(
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
) -> Dict[str, Any]:
    """Get pipeline summary. Requires authentication."""
    return get_pipeline_summary(db)

# Lead Notes endpoints
@router.post("/{lead_id}/notes", response_model=LeadNoteRead)
def add_lead_note(
    lead_id: int,
    payload: LeadNoteCreate,
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """Add a note to a lead. Requires authentication."""
    lead = get_lead(db, lead_id)
    if not lead:
        raise HTTPException(status_code=404, detail="Lead not found")
    payload.lead_id = lead_id
    return create_lead_note(db, payload)

@router.get("/{lead_id}/notes", response_model=List[LeadNoteRead])
def list_lead_notes(
    lead_id: int,
    db: Session = Depends(get_session),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user=Depends(get_current_user)
):
    """List notes for a lead with pagination. Requires authentication."""
    return get_lead_notes(db, lead_id, skip=skip, limit=limit)

@router.delete("/notes/{note_id}", status_code=200)
def delete_lead_note_endpoint(
    note_id: int,
    db: Session = Depends(get_session),
    current_user=Depends(get_current_user)
):
    """Delete a lead note. Admin only. Returns a confirmation message."""
    if not delete_lead_note(db, note_id):
        raise HTTPException(status_code=404, detail="Note not found")
    return {"detail": f"Note {note_id} deleted successfully"}
